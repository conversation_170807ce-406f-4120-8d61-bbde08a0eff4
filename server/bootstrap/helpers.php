<?php

use Carbon\Carbon;
use Illuminate\Support\Facades\Lang;
use <PERSON>patser\Uuid\Uuid;

if (!function_exists('constants')) {
    function constants($key)
    {
        return config('constants.'.$key);
    }
}

if (!function_exists('carbon_create_format')) {
    function carbon_create_format($format, $time): Carbon
    {
        return Carbon::createFromFormat($format, $time);
    }
}

if (!function_exists('carbon_parse')) {
    function carbon_parse($time, $timezone = 'Asia/Yerevan'): Carbon
    {
        return Carbon::parse($time, $timezone);
    }
}

if (!function_exists('generate_uuid')) {
    function generate_uuid(): string
    {
        return Uuid::generate(4)->string;
    }
}

if (!function_exists('lang')) {
    function lang($key, $replace = [], $locale = 'hy')
    {
        return Lang::get('lang.'.$key, $replace, $locale);
    }
}

if (!function_exists('is_dev')) {
    function is_dev()
    {
        return !app()->environment('production');
    }
}
