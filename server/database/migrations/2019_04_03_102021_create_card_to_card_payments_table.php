<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCardToCardPaymentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('card_to_card_payments', function (Blueprint $table) {
            $table->increments('id');
            $table->string('card_number');
            $table->string('embossed_name');
            $table->string('year');
            $table->string('month');
            $table->string('partner_identificator')->nullable();
            $table->date('paid')->nullable();
            $table->boolean('withdrawn')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('card_to_card_payments');
    }
}
