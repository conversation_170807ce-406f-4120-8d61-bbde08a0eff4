<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateLoanTypeTransferTypePivotTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('loan_type_transfer_type', function (Blueprint $table) {
            $table->integer('loan_type_id')->unsigned()->index();
            $table->foreign('loan_type_id')->references('id')->on('loan_types')->onDelete('cascade');
            $table->integer('transfer_type_id')->unsigned()->index();
            $table->foreign('transfer_type_id')->references('id')->on('transfer_types')->onDelete('cascade');
            $table->primary(['loan_type_id', 'transfer_type_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('loan_type_transfer_type');
    }
}
