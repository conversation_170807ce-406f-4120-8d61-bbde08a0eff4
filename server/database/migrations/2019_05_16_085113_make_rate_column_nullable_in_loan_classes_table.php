<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class MakeRateColumnNullableInLoanClassesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('loan_classes', function (Blueprint $table) {
            $table->float('rate')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::table('loan_classes')->whereNull('rate')->delete();

        Schema::table('loan_classes', function (Blueprint $table) {
            $table->float('rate')->nullable(false)->change();
        });
    }
}
