<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateLoanTypeRulePivotTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('loan_type_rule', function (Blueprint $table) {
            $table->integer('loan_type_id')->unsigned()->index();
            $table->foreign('loan_type_id')->references('id')->on('loan_types')->onDelete('cascade');
            $table->integer('rule_id')->unsigned()->index();
            $table->foreign('rule_id')->references('id')->on('rules')->onDelete('cascade');
            $table->primary(['loan_type_id', 'rule_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('loan_type_rule');
    }
}
