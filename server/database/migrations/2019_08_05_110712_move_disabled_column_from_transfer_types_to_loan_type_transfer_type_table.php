<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class MoveDisabledColumnFromTransferTypesToLoanTypeTransferTypeTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('loan_type_transfer_type', function (Blueprint $table) {
            $table->boolean('disabled')->default(false);
        });

        Schema::table('transfer_types', function (Blueprint $table) {
            $table->dropColumn('disabled');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('loan_type_transfer_type', function (Blueprint $table) {
            $table->dropColumn('disabled');
        });

        Schema::table('transfer_types', function (Blueprint $table) {
            $table->boolean('disabled')->default(false);
        });
    }
}
