<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class DropGcIdramEasypayUpayPaymentsTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::dropIfExists('gc_cash_payments');
        Schema::dropIfExists('idram_payments');
        Schema::dropIfExists('easypay_payments');
        Schema::dropIfExists('upay_payments');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::create('gc_cash_payments', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('cash_office_id');
            $table->date('paid')->nullable();
            $table->boolean('withdrawn')->default(false);
            $table->timestamps();
        });

        Schema::create('idram_payments', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('cash_office_id');
            $table->date('paid')->nullable();
            $table->boolean('withdrawn')->default(false);
            $table->timestamps();
        });

        Schema::create('easypay_payments', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('cash_office_id');
            $table->date('paid')->nullable();
            $table->boolean('withdrawn')->default(false);
            $table->timestamps();
        });

        Schema::create('upay_payments', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('cash_office_id');
            $table->date('paid')->nullable();
            $table->boolean('withdrawn')->default(false);
            $table->timestamps();
        });
    }
}
