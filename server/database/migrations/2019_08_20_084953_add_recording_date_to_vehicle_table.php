<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddRecordingDateToVehicleTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('vehicles', function (Blueprint $table) {
            $table->dateTime('recording_date')->nullable();
            $table->integer('released')->nullable();
            $table->string('vehicle_type')->nullable();
            $table->string('body_type')->nullable();
            $table->string('engine_num')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('vehicles', function (Blueprint $table) {
            $table->dropColumn('recording_date');
            $table->dropColumn('released');
            $table->dropColumn('vehicle_type');
            $table->dropColumn('body_type');
            $table->dropColumn('engine_num');
        });
    }
}
