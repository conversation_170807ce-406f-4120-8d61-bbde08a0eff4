<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateVehicleConditionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('vehicle_conditions', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('mortgage_id');
            $table->boolean('external_defects')->nullable();
            $table->string('external_defects_comment')->nullable();
            $table->boolean('interior_defects')->nullable();
            $table->string('interior_defects_comment')->nullable();
            $table->boolean('engine_defects')->nullable();
            $table->string('engine_defects_comment')->nullable();
            $table->boolean('transmission_defects')->nullable();
            $table->string('transmission_defects_comment')->nullable();
            $table->boolean('light_sensors')->nullable();
            $table->string('light_sensors_comment')->nullable();
            $table->string('mileage')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('vehicle_conditions');
    }
}
