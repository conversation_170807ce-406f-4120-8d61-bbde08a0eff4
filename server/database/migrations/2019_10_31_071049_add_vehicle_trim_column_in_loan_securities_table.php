<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddVehicleTrimColumnInLoanSecuritiesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('loan_securities', function (Blueprint $table) {
            $table->string('vehicle_trim')->nullable();
        });

        Schema::table('vehicle_models', function (Blueprint $table) {
            $table->dropColumn('visible');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('loan_securities', function (Blueprint $table) {
            $table->dropColumn('vehicle_trim');
        });

        Schema::table('vehicle_models', function (Blueprint $table) {
            $table->boolean('visible')->nullable()->default(false);
        });
    }
}
