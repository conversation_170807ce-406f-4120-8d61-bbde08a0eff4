<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCheckupDateFieldToMortgagesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('mortgages', function (Blueprint $table) {
            $table->dateTime('checkup_date')->nullable();
            $table->dropColumn('from');
            $table->dropColumn('to');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('mortgages', function (Blueprint $table) {
            $table->dropColumn('checkup_date');
            $table->dateTime('from')->nullable();
            $table->dateTime('to')->nullable();
        });
    }
}
