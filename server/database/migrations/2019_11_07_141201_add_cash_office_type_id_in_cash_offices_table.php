<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCashOfficeTypeIdInCashOfficesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('cash_offices', function (Blueprint $table) {
            $table->integer('cash_office_type_id');
            $table->dropColumn('name');
            $table->dropColumn('disabled');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('cash_offices', function (Blueprint $table) {
            $table->dropColumn('cash_office_type_id');
            $table->string('name');
            $table->boolean('disabled')->default(false);
        });
    }
}
