<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateCashOfficeTypeLoanTypePivotTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('cash_office_type_loan_type', function (Blueprint $table) {
            $table->integer('cash_office_type_id')->unsigned()->index();
            $table->foreign('cash_office_type_id')->references('id')->on('cash_office_types')->onDelete('cascade');
            $table->integer('loan_type_id')->unsigned()->index();
            $table->foreign('loan_type_id')->references('id')->on('loan_types')->onDelete('cascade');
            $table->primary(['cash_office_type_id', 'loan_type_id']);
            $table->boolean('disabled')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('cash_office_type_loan_type');
    }
}
