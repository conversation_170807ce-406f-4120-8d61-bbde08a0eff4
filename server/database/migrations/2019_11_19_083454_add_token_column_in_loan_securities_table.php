<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddTokenColumnInLoanSecuritiesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('loan_securities', function (Blueprint $table) {
            $table->string('identity_verification_token')->nullable();
            $table->dateTime('identity_verification_token_sent')->nullable();
            $table->dateTime('identity_verification_token_exp')->nullable();
            $table->string('phone_number')->nullable();
            $table->string('email')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('loan_securities', function (Blueprint $table) {
            $table->dropColumn('identity_verification_token');
            $table->dropColumn('phone_number');
            $table->dropColumn('email');
        });
    }
}
