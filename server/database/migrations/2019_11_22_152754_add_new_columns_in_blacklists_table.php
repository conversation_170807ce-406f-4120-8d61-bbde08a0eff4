<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddNewColumnsInBlacklistsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('blacklists', function (Blueprint $table) {
            $table->string('credit_card')->nullable();
            $table->string('phone_number')->nullable();
            $table->string('email')->nullable();
            $table->dateTime('expiration')->nullable();
        });

        Schema::table('loan_securities', function (Blueprint $table) {
            $table->string('ssn')->nullable();
            $table->string('vin')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('blacklists', function (Blueprint $table) {
            $table->dropColumn('credit_card');
            $table->dropColumn('phone_number');
            $table->dropColumn('email');
            $table->dropColumn('expiration');
        });

        Schema::table('loan_securities', function (Blueprint $table) {
            $table->dropColumn('ssn');
            $table->dropColumn('vin');
        });
    }
}
