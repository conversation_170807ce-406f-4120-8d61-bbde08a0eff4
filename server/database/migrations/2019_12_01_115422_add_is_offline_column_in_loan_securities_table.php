<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIsOfflineColumnInLoanSecuritiesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('loan_securities', function (Blueprint $table) {
            $table->boolean('is_offline')->default(false)->nullable();
        });

        Schema::table('loans', function (Blueprint $table) {
            $table->boolean('is_offline')->default(false)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('loan_securities', function (Blueprint $table) {
            $table->dropColumn('is_offline');
        });

        Schema::table('loans', function (Blueprint $table) {
            $table->dropColumn('is_offline');
        });
    }
}
