<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCitizenFaceRecognitionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('citizen_face_recognitions', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('loan_security_id')->nullable();
            $table->string('liveness_rate')->nullable();
            $table->string('face_similarity')->nullable();
            $table->string('image_path')->nullable();
            $table->string('status');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('citizen_face_recognitions');
    }
}
