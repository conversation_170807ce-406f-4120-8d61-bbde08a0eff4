<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddRateFeeInLoanSchedulesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('loan_schedules', function (Blueprint $table) {
            $table->float('service_fee_interest')->nullable();
            $table->float('service_fee_plain')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('loan_schedules', function (Blueprint $table) {
            $table->dropColumn('service_fee_interest');
            $table->dropColumn('service_fee_plain');
        });
    }
}
