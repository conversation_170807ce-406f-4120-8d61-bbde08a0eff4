<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddImageWidthToCitizenFaceRecognitionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('citizen_face_recognitions', function (Blueprint $table) {
            $table->integer('image_width')->nullable();
            $table->integer('image_height')->nullable();
            $table->integer('eye_distance')->nullable();
            $table->float('quality')->nullable();
            $table->float('head_ratio_width')->nullable();
            $table->float('head_ratio_height')->nullable();
            $table->boolean('extended_liveness')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('citizen_face_recognitions', function (Blueprint $table) {
            $table->dropColumn('image_width');
            $table->dropColumn('image_height');
            $table->dropColumn('eye_distance');
            $table->dropColumn('quality');
            $table->dropColumn('extended_liveness');
            $table->dropColumn('head_ratio_width');
            $table->dropColumn('head_ratio_height');
        });
    }
}
