<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSolarPanelMortgagesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('solar_panel_mortgages', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('loan_id');
            $table->string('contract_number');
            $table->string('sales_contract_number');
            $table->timestamps();

            $table->foreign('loan_id')
                ->references('id')
                ->on('loans');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('solar_panel_mortgages');
    }
}
