<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddTradeAmountColumnInMortgagesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('mortgages', function (Blueprint $table) {
            $table->float('trade_amount')->nullable();
            $table->string('tech_passport')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('mortgages', function (Blueprint $table) {
            $table->dropColumn('trade_amount');
            $table->dropColumn('tech_passport');
        });
    }
}
