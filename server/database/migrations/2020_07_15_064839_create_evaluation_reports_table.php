<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEvaluationReportsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('evaluation_reports', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('solar_panel_id');
            $table->string('evaluation_id');
            $table->dateTime('evaluation_date');
            $table->decimal('bonus_amount', 13, 2);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('evaluation_reports');
    }
}
