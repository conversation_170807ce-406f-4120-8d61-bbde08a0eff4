<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddInterestRateColumnInPipeTypesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('pipe_types', function (Blueprint $table) {
            $table->integer('duration')->nullable();
            $table->float('rate')->nullable();
        });

        Schema::table('loan_securities', function (Blueprint $table) {
            $table->integer('pipe_type_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('pipe_types', function (Blueprint $table) {
            $table->dropColumn('duration');
            $table->dropColumn('rate');
        });

        Schema::table('loan_securities', function (Blueprint $table) {
            $table->dropColumn('pipe_type_id');
        });
    }
}
