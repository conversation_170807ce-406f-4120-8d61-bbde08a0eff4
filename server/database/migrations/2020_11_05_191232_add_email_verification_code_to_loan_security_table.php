<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddEmailVerificationCodeToLoanSecurityTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('loan_securities', function (Blueprint $table) {
            $table->string('email_verification_code')->nullable();
            $table->dateTime('email_verification_code_sent')->nullable();
            $table->dateTime('email_verification_code_exp')->nullable();
            $table->dateTime('email_verification_code_recv')->nullable();
            $table->integer('email_verification_code_attempts')->default(0);
            $table->integer('get_email_verification_code_attempts')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('loan_securities', function (Blueprint $table) {
            $table->dropColumn('email_verification_code');
            $table->dropColumn('email_verification_code_sent');
            $table->dropColumn('email_verification_code_exp');
            $table->dropColumn('email_verification_code_recv');
            $table->dropColumn('email_verification_code_attempts');
            $table->dropColumn('get_email_verification_code_attempts');
        });
    }
}
