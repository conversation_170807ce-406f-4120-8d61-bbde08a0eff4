<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddTermIdInSolarPanelsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('solar_panels', function (Blueprint $table) {
            $table->integer('term_id')->nullable();

            $table->foreign('term_id')
                ->references('id')
                ->on('arpi_solar_terms')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('solar_panels', function (Blueprint $table) {
            $table->dropColumn('term_id');
        });
    }
}
