<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTransactionSchedulesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('transaction_schedules', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('transaction_id');
            $table->foreign('transaction_id')->references('id')->on('transactions');
            $table->float('service_fee');
            $table->float('base');
            $table->float('payment');
            $table->float('total');
            $table->dateTime('date');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('transaction_schedules');
    }
}
