<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdatePaymentDatesInPaymentTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('cash_payments', function (Blueprint $table) {
            $table->dateTime('paid')->nullable()->change();
        });

        Schema::table('card_to_card_payments', function (Blueprint $table) {
            $table->dateTime('paid')->nullable()->change();
        });

        Schema::table('idram_wallet_payments', function (Blueprint $table) {
            $table->dateTime('paid')->nullable()->change();
        });

        Schema::table('easypay_wallet_payments', function (Blueprint $table) {
            $table->dateTime('paid')->nullable()->change();
            $table->dateTime('withdrawn')->nullable()->change();
        });

        Schema::table('product_provisions', function (Blueprint $table) {
            $table->dateTime('paid')->nullable()->change();
            $table->dateTime('withdrawn')->nullable()->change();
        });

        Schema::table('idram_loan_payments', function (Blueprint $table) {
            $table->dateTime('paid')->nullable()->change();
            $table->dateTime('withdrawn')->nullable()->change();
        });

        Schema::table('wire_payments', function (Blueprint $table) {
            $table->dateTime('paid')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('cash_payments', function (Blueprint $table) {
            $table->date('paid')->nullable()->change();
        });

        Schema::table('card_to_card_payments', function (Blueprint $table) {
            $table->date('paid')->nullable()->change();
        });

        Schema::table('idram_wallet_payments', function (Blueprint $table) {
            $table->date('paid')->nullable()->change();
        });

        Schema::table('easypay_wallet_payments', function (Blueprint $table) {
            $table->date('paid')->nullable()->change();
            $table->date('withdrawn')->nullable()->change();
        });

        Schema::table('product_provisions', function (Blueprint $table) {
            $table->date('paid')->nullable()->change();
            $table->date('withdrawn')->nullable()->change();
        });

        Schema::table('idram_loan_payments', function (Blueprint $table) {
            $table->date('paid')->nullable()->change();
            $table->date('withdrawn')->nullable()->change();
        });

        Schema::table('wire_payments', function (Blueprint $table) {
            $table->date('paid')->nullable()->change();
        });
    }
}
