<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddPhaseColumnInRulesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('rules', function (Blueprint $table) {
            $table->integer('phase')->default(1);
            $table->string('label')->nullable();
            $table->integer('rule_set_id');

            $table->unique(['label', 'rule_set_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('rules', function (Blueprint $table) {
            $table->dropColumn('phase');
            $table->dropColumn('label');
            $table->dropColumn('rule_set_id');

            $table->dropUnique('rules_label_rule_set_id_unique');
        });
    }
}
