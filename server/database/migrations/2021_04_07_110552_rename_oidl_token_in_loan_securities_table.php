<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class RenameOidlTokenInLoanSecuritiesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('loan_securities', function (Blueprint $table) {
            $table->renameColumn('oidl_token', 'owl_token');
        });

        Schema::rename('idram_details', 'wallet_details');
        Schema::rename('idram_loan_payments', 'wallet_loan_payments');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('loan_securities', function (Blueprint $table) {
            $table->renameColumn('owl_token', 'oidl_token');
        });

        Schema::rename('wallet_details', 'idram_details');
        Schema::rename('wallet_loan_payments', 'idram_loan_payments');
    }
}
