<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateForgotPasswordDetailsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('forgot_password_details', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('user_id')->nullable();
            $table->string('email_verification_code')->nullable();
            $table->dateTime('email_verification_code_sent')->nullable();
            $table->dateTime('email_verification_code_exp')->nullable();
            $table->dateTime('email_verification_code_recv')->nullable();
            $table->integer('get_email_verification_code_attempts')->default(0);
            $table->string('sms_verification_code')->nullable();
            $table->dateTime('sms_verification_code_sent')->nullable();
            $table->dateTime('sms_verification_code_exp')->nullable();
            $table->dateTime('sms_verification_code_recv')->nullable();
            $table->integer('get_sms_verification_code_attempts')->default(0);
            $table->integer('code_verification_attempts')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('forgot_password_details');
    }
}
