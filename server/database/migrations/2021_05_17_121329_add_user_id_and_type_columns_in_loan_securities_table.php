<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddUserIdAndTypeColumnsInLoanSecuritiesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('loan_securities', function (Blueprint $table) {
            $table->integer('user_id')->nullable();
            $table->string('type')->default('loan_application');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('loan_securities', function (Blueprint $table) {
            $table->dropColumn('user_id');
            $table->dropColumn('type');
        });
    }
}
