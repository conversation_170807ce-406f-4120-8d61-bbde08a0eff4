<?php

use App\Models\CreditLine\PurchaseSession;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddStatusToPurchaseSessionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('purchase_sessions', function (Blueprint $table) {
            $table->string('status')->nullable();
        });

        $purchase_sessions = PurchaseSession::all();
        $purchase_service = resolve('App\Interfaces\CreditLine\IPurchaseService');

        foreach ($purchase_sessions as $purchase_session) {
            $transaction = $purchase_service->resolveTransaction($purchase_session->payment_id);

            $purchase_session->update(['status' => $transaction ? PurchaseSession::CONFIRMED : PurchaseSession::REJECTED]);
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('purchase_sessions', function (Blueprint $table) {
            $table->dropColumn('status');
        });
    }
}
