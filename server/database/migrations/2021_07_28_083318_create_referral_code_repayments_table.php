<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateReferralCodeRepaymentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('referral_code_repayments', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('referral_code_id');
            $table->string('contract_number');
            $table->integer('loan_type_id');
            $table->float('amount');
            $table->timestamps();

            $table->foreign('referral_code_id')
            ->references('id')
            ->on('referral_codes')
            ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('referral_code_repayments');
    }
}
