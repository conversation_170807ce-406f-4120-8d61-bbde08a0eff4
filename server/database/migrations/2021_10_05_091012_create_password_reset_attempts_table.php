<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreatePasswordResetAttemptsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
        {
        Schema::create('password_reset_attempts', function (Blueprint $table) {
            $table->increments('id');
            $table->string('phone_number');
            $table->string('verification_code')->nullable();
            $table->dateTime('verification_code_sent')->nullable();
            $table->dateTime('verification_code_exp')->nullable();
            $table->dateTime('verification_code_recv')->nullable();
            $table->integer('verification_code_attempts')->default(0);
            $table->string('uuid');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('password_reset_attempts');
    }
}
