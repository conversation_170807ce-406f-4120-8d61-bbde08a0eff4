<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRealEstateDetailsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('real_estate_details', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('loan_security_id')->nullable();
            $table->integer('region_id')->nullable();
            $table->string('address')->nullable();
            $table->float('price')->nullable();
            $table->timestamps();

            $table->foreign('loan_security_id')
                ->references('id')
                ->on('loan_securities')
                ->onDelete('cascade');

            $table->foreign('region_id')
                ->references('id')
                ->on('regions')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('real_estate_details');
    }
}
