<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRealEstateMediaTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('real_estate_media', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('real_estate_detail_id')->nullable();
            $table->string('path');
            $table->string('type');
            $table->string('name');
            $table->timestamps();

            $table->foreign('real_estate_detail_id')
                ->references('id')
                ->on('real_estate_details')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('real_estate_media');
    }
}
