<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRealEstateMortgagesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('real_estate_mortgages', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('loan_id');
            $table->string('contract_number')->nullable();
            $table->float('prepayment')->nullable();
            $table->integer('evaluation_company_id')->nullable();
            $table->timestamps();

            $table->foreign('loan_id')
                ->references('id')
                ->on('loans');

            $table->foreign('evaluation_company_id')
                ->references('id')
                ->on('real_estate_evaluation_companies')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('real_estate_mortgages');
    }
}
