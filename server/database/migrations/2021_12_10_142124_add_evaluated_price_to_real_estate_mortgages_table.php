<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddEvaluatedPriceToRealEstateMortgagesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('real_estate_mortgages', function (Blueprint $table) {
            $table->float('evaluated_price')->nullable();
            $table->date('evaluated_date')->nullable();
            $table->string('subject_type')->nullable();
            $table->string('subject_address')->nullable();
            $table->text('subject_description')->nullable();
            $table->date('ownership_cert_given_date')->nullable();
            $table->string('ownership_cert_number')->nullable();
            $table->string('ownership_cert_password')->nullable();
            $table->date('unified_cadastral_ref_given_date')->nullable();
            $table->string('unified_cadastral_ref_number')->nullable();
            $table->string('unified_cadastral_ref_password')->nullable();
            $table->text('ec_admin_notes')->nullable();
            $table->text('gc_admin_notes')->nullable();
            $table->text('gc_sub_admin_notes')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('real_estate_mortgages', function (Blueprint $table) {
            $table->dropColumn('evaluated_price');
            $table->dropColumn('evaluated_date');
            $table->dropColumn('subject_type');
            $table->dropColumn('subject_address');
            $table->dropColumn('subject_description');
            $table->dropColumn('ownership_cert_given_date');
            $table->dropColumn('ownership_cert_number');
            $table->dropColumn('unified_cadastral_ref_given_date');
            $table->dropColumn('unified_cadastral_ref_number');
            $table->dropColumn('ec_admin_notes');
            $table->dropColumn('gc_admin_notes');
            $table->dropColumn('gc_sub_admin_notes');
        });
    }
}
