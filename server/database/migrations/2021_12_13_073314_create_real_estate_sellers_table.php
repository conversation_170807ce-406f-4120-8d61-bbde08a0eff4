<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRealEstateSellersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('real_estate_sellers', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('real_estate_mortgage_id')->nullable();
            $table->string('first_name')->nullable();
            $table->string('last_name')->nullable();
            $table->string('middle_name')->nullable();
            $table->string('phone_number', 20)->nullable();
            $table->date('birthday')->nullable();
            $table->string('passport_number', 9)->nullable();
            $table->date('given_date')->nullable();
            $table->string('from', 3)->nullable();
            $table->string('registration_address')->nullable();
            $table->string('bank', 50)->nullable();
            $table->string('bank_account', 16)->nullable();
            $table->timestamps();

            $table->foreign('real_estate_mortgage_id')
                ->references('id')
                ->on('real_estate_mortgages')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('real_estate_sellers');
    }
}
