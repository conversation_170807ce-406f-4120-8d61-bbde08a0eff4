<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddSremlMortgageContractNotesToRealEstateDetailsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('real_estate_details', function (Blueprint $table) {
            $table->integer('sreml_mortgage_contract_quantity')->nullable();
            $table->text('sreml_mortgage_contract_notes')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('real_estate_details', function (Blueprint $table) {
            $table->dropColumn('sreml_mortgage_contract_quantity');
            $table->dropColumn('sreml_mortgage_contract_notes');
        });
    }
}
