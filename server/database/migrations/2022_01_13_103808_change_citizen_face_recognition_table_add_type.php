<?php

use App\Models\CitizenFaceRecognition;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ChangeCitizenFaceRecognitionTableAddType extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('citizen_face_recognitions', function (Blueprint $table) {
            $table->string('type')->default(CitizenFaceRecognition::CAMERA_PHOTO);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('citizen_face_recognitions', function (Blueprint $table) {
            $table->dropColumn('type');
        });
    }
}
