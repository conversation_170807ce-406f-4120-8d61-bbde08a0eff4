<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddColumnsInPredefinedRealEstatesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('predefined_real_estates', function (Blueprint $table) {
            $table->float('area')->nullable();
            $table->integer('apartment_id')->nullable();
            $table->string('building')->nullable();
            $table->date('building_deadline')->nullable();
            $table->string('apartment_number')->nullable();
            $table->string('room_count')->nullable();
            $table->string('floor')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('predefined_real_estates', function (Blueprint $table) {
            $table->dropColumn([
                'area',
                'apartment_id',
                'building',
                'building_deadline',
                'apartment_number',
                'room_count',
                'floor',
            ]);
        });
    }
}
