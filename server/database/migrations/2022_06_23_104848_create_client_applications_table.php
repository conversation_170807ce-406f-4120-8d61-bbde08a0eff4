<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateClientApplicationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('client_applications', function (Blueprint $table) {
            $table->increments('id');
            $table->string('ssn');
            $table->string('phone_number');
            $table->string('email');
            $table->string('uuid')->unique()->index();
            $table->string('status');
            $table->integer('ekeng_request_id')->nullable();
            $table->timestamps();

            $table->foreign('ekeng_request_id')
                ->references('id')
                ->on('ekeng_requests');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('client_applications');
    }
}
