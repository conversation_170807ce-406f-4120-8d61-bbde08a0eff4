<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ChangeClientApplicationsTableAddSmsEmailFields extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('client_applications', function (Blueprint $table) {
            $table->integer('sent_email_count')->nullable();
            $table->integer('sent_sms_count')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('client_applications', function (Blueprint $table) {
            $table->dropColumns(['is_email_sent', 'is_sms_sent']);
        });
    }
}
