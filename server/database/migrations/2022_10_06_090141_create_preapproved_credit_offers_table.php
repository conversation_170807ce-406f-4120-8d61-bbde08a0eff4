<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePreapprovedCreditOffersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('preapproved_credit_offers', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('legal_entity_id');
            $table->dateTime('expiration_date')->nullable();
            $table->integer('loan_type_id');
            $table->integer('rule_set_id');
            $table->float('amount')->nullable();
            $table->integer('duration')->nullable();
            $table->float('rate')->nullable();
            $table->float('service_fee_rate')->nullable();
            $table->float('score')->nullable();
            $table->boolean('rejected')->nullable();
            $table->timestamps();

            $table->foreign('legal_entity_id')
                ->references('id')
                ->on('legal_entities')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('preapproved_credit_offers');
    }
}
