<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePreapprovedAffectedRulesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('preapproved_affected_rules', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('preapproved_credit_offer_id');
            $table->integer('rule_id');
            $table->boolean('rejected')->nullable();
            $table->float('amount', 500)->nullable();
            $table->integer('duration')->nullable();
            $table->float('rate')->nullable();
            $table->float('service_fee_rate')->nullable();
            $table->string('label')->nullable();
            $table->float('score')->nullable();
            $table->timestamps();

            $table->foreign('preapproved_credit_offer_id')
                ->references('id')
                ->on('preapproved_credit_offers')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('preapproved_affected_rules');
    }
}
