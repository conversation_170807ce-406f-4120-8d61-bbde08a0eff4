<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddLoanSecurityIdInPreapprovedCreditOffersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('preapproved_credit_offers', function (Blueprint $table) {
            $table->integer('loan_security_id')->nullable();
            $table->string('type')->nullable();

            $table->foreign('loan_security_id')
                ->references('id')
                ->on('loan_securities')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('preapproved_credit_offers', function (Blueprint $table) {
            $table->dropColumn('loan_security_id');
            $table->dropColumn('type');
        });
    }
}
