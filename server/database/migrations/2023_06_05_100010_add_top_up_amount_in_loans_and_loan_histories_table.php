<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddTopUpAmountInLoansAndLoanHistoriesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('loans', function (Blueprint $table) {
            $table->float('top_up_amount')->nullable();
        });
        Schema::table('loan_histories', function (Blueprint $table) {
            $table->float('top_up_amount')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('loans', function (Blueprint $table) {
            $table->dropColumn('top_up_amount');
        });
        Schema::table('loan_histories', function (Blueprint $table) {
            $table->dropColumn('top_up_amount');
        });
    }
}
