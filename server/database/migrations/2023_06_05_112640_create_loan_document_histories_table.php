<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLoanDocumentHistoriesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('loan_document_histories', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('loan_id');
            $table->integer('loan_history_id')->nullable();
            $table->string('path');
            $table->string('document_type');
            $table->boolean('public');
            $table->timestamps();

            $table->foreign('loan_id')
                ->references('id')
                ->on('loans')
                ->onDelete('cascade');
            $table->foreign('loan_history_id')
                ->references('id')
                ->on('loan_histories')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('loan_document_histories');
    }
}
