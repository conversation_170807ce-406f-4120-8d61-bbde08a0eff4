<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddBatchInLoanDocumentsAndLoanDocumentHistoriesTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('loan_documents', function (Blueprint $table) {
            $table->integer('batch')->nullable();
        });
        Schema::table('loan_document_histories', function (Blueprint $table) {
            $table->integer('batch')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('loan_documents', function (Blueprint $table) {
            $table->dropColumn('batch');
        });
        Schema::table('loan_document_histories', function (Blueprint $table) {
            $table->dropColumn('batch');
        });
    }
}
