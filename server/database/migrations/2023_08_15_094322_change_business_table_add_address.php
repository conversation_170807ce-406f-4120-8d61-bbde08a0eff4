<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class ChangeBusinessTableAddAddress extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('businesses', function (Blueprint $table) {
            $table->string('address')->nullable();
            $table->string('apartment')->nullable();
            $table->string('region')->nullable();
            $table->string('community')->nullable();
            $table->string('location')->nullable();
            $table->string('city')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('businesses', function (Blueprint $table) {
            $table->dropColumn('address');
            $table->dropColumn('apartment');
            $table->dropColumn('region');
            $table->dropColumn('community');
            $table->dropColumn('location');
            $table->dropColumn('city');
        });
    }
}
