<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddProductColumnsToVeloxDetails extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('velox_details', function (Blueprint $table) {
            $table->integer('loan_id')->nullable()->change();
            $table->string('lender_token')->nullable()->change();
            $table->integer('product_category_id')->nullable();
            $table->string('product_category_name')->nullable();
            $table->string('product_merchant_name')->nullable();
            $table->string('product_merchant_address')->nullable();
            $table->integer('loan_security_id')->nullable();

            $table->foreign('loan_security_id')->references('id')->on('loan_securities')->onDelete('cascade');
            $table->index(['loan_security_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('velox_details', function (Blueprint $table) {
            $table->dropColumn('product_category_id');
            $table->dropColumn('product_category_name');
            $table->dropColumn('product_merchant_name');
            $table->dropColumn('product_merchant_address');
            $table->dropColumn('loan_security_id');
        });
    }
}
