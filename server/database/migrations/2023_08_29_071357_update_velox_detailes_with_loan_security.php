<?php

use App\Models\LoanSecurity;
use App\Models\VeloxDetail;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateVeloxDetailesWithLoanSecurity extends Migration
{
    const CHUNK_SIZE = 500;

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        VeloxDetail::chunk(self::CHUNK_SIZE, function ($velox_details) {
            foreach ($velox_details as $velox_detail) {
                $loan_security = LoanSecurity::where('loan_id', $velox_detail->loan_id)->first();

                if ($loan_security) {
                    $velox_detail->update(['loan_security_id' => $loan_security->id]);
                }
            }
        });

        Schema::table('velox_details', function (Blueprint $table) {
            $table->dropColumn('loan_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
}
