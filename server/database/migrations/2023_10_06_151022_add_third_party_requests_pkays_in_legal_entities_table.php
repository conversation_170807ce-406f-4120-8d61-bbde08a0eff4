<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddThirdPartyRequestsPkaysInLegalEntitiesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('legal_entities', function (Blueprint $table) {
            $table->string('phone_number')->nullable();
            $table->string('email')->nullable();
            $table->integer('ekeng_request_pkey')->nullable();
            $table->integer('nork_request_pkey')->nullable();
            $table->integer('acra_request_pkey')->nullable();

            $table->index(['ekeng_request_pkey']);
            $table->index(['nork_request_pkey']);
            $table->index(['acra_request_pkey']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('legal_entities', function (Blueprint $table) {
            $table->dropColumn('phone_number');
            $table->dropColumn('email');
            $table->dropColumn('ekeng_request_pkey');
            $table->dropColumn('nork_request_pkey');
            $table->dropColumn('acra_request_pkey');
        });
    }
}
