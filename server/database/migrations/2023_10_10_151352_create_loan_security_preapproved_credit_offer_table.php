<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLoanSecurityPreapprovedCreditOfferTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('loan_security_preapproved_credit_offer', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('loan_security_id')->unsigned()->index();
            $table->integer('preapproved_credit_offer_id')->unsigned()->index();
            $table->timestamps();

            $table->foreign('loan_security_id')->references('id')->on('loan_securities')->onDelete('cascade');
            $table->foreign('preapproved_credit_offer_id')->references('id')->on('preapproved_credit_offers')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('loan_security_preapproved_credit_offer');
    }
}
