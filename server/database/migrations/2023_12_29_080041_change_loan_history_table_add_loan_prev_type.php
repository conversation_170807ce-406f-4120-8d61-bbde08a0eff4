<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class   ChangeLoanHistoryTableAddLoanPrevType extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('loan_histories', function (Blueprint $table) {
            $table->integer('prev_loan_type_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('loan_histories', function (Blueprint $table) {
            $table->dropColumn('prev_loan_type_id');
        });
    }
}
