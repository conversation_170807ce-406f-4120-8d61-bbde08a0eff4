<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateVeloxLoanOfferDetailsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('velox_loan_offer_details', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('loan_security_id')->unsigned()->index();
            $table->string('contract_number')->nullable();
            $table->string('credit_code')->nullable();
            $table->timestamps();

            $table->foreign('loan_security_id')->references('id')->on('loan_securities')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('velox_loan_offer_details');
    }
}
