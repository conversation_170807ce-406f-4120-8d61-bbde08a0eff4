<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CreateLoanDisbursementsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('loan_disbursements', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedBigInteger('loan_security_id')->index();
            $table->string('disbursement_type');
            $table->unsignedBigInteger('disbursement_id');
            $table->timestamps();

            $table->unique(['disbursement_type', 'disbursement_id'], 'unique_disbursement');
        });

        DB::statement('ALTER SEQUENCE loan_disbursements_id_seq RESTART WITH 100000;');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('loan_disbursements');
    }
}
