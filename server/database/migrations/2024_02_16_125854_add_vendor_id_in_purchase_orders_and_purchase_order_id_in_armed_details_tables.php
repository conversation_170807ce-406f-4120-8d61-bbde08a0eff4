<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddVendorIdInPurchaseOrdersAndPurchaseOrderIdInArmedDetailsTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('purchase_orders', function (Blueprint $table) {
            $table->integer('vendor_id')->nullable();
            $table->string('hash_ssn')->nullable();
            $table->string('document_number')->nullable()->change();
            $table->string('ssn')->nullable()->change();

            $table->foreign('vendor_id')->references('id')->on('vendors')->onDelete('cascade');
        });

        Schema::table('armed_details', function (Blueprint $table) {
            $table->integer('purchase_order_id')->nullable();
            $table->string('status')->nullable()->default(null)->change();
            $table->foreign('purchase_order_id')->references('id')->on('purchase_orders')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('purchase_orders', function (Blueprint $table) {
            $table->dropColumn('vendor_id');
            $table->dropColumn('hash_ssn');
            $table->string('document_number')->nullable(false)->change();
            $table->string('ssn')->nullable(false)->change();
        });
        Schema::table('armed_details', function (Blueprint $table) {
            $table->dropColumn('purchase_order_id');
            $table->string('status')->nullable(false)->default('PENDING')->change();
        });
    }
}
