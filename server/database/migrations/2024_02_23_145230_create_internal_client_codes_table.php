<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateInternalClientCodesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('internal_client_codes', function (Blueprint $table) {
            $table->increments('id');
            $table->string('ssn');
            $table->string('client_type', 2); // because we will keep 21 or 22
            $table->string('client_id', 20)->unique()->index(); //for now the length will be 6 digits, string because we don't know central bank what will be suggest after a year, and also it can start with 0s
            $table->boolean('is_hc_inserted')->default(false);
            $table->timestamps();

            // Unique constraint for (ssn, client_type) pair
            $table->unique(['ssn', 'client_type']);
            $table->index(['ssn', 'client_type']);
        });

        DB::statement('CREATE SEQUENCE IF NOT EXISTS client_id_seq START WITH 500000 INCREMENT BY 1;');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('internal_client_codes');
    }
}
