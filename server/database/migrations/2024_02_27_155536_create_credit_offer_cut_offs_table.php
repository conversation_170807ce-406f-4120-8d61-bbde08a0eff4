<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCreditOfferCutOffsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('credit_offer_cut_offs', function (Blueprint $table) {
            $table->increments('id');
            $table->string('ssn');
            $table->integer('loan_security_id')->nullable();
            $table->foreign('loan_security_id')->references('id')->on('loan_securities');
            $table->integer('loan_type_id')->nullable();
            $table->string('reason');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('credit_offer_cut_offs');
    }
}
