<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddReasonIdToBlacklistsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('blacklists', function (Blueprint $table) {
            $table->integer('unblock_reason_id')->nullable();
            $table->integer('block_reason_id')->nullable();
            $table->text('unblock_note')->nullable();
            $table->text('block_note')->nullable();
            $table->foreign('unblock_reason_id')->references('id')->on('blacklist_reasons');
            $table->foreign('block_reason_id')->references('id')->on('blacklist_reasons');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('blacklists', function (Blueprint $table) {
            $table->dropColumn('unblock_reason_id');
            $table->dropColumn('block_reason_id');
            $table->dropColumn('unblock_note');
            $table->dropColumn('block_note');
        });
    }
}
