<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddNameHyAndPhoneNumberColumnsInCashOfficeTypesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('cash_office_types', function (Blueprint $table) {
            $table->string('name_hy')->nullable();
            $table->string('phone_number')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('cash_office_types', function (Blueprint $table) {
            $table->dropColumn('name_hy');
            $table->dropColumn('phone_number');
        });
    }
}
