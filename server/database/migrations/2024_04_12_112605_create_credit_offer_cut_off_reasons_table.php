<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schema;

class CreateCreditOfferCutOffReasonsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('credit_offer_cut_off_reasons', function (Blueprint $table) {
            $table->increments('id');
            $table->string('name');
        });

        Artisan::call('db:seed', [
            '--class' => 'CreditOfferCutOffReasonsTableSeeder',
            '--force' => true,
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('credit_offer_cut_off_reasons');
    }
}
