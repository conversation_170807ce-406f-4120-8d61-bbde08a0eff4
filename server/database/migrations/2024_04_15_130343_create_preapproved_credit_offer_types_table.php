<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schema;

class CreatePreApprovedCreditOfferTypesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('preapproved_credit_offer_types', function (Blueprint $table) {
            $table->increments('id');
            $table->string('name');
        });

        Artisan::call('db:seed', [
            '--class' => 'PreapprovedCreditOfferTypesTableSeeder',
            '--force' => true,
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('preapproved_credit_offer_types');
    }
}
