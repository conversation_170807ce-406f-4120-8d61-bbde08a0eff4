<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class AddCreditOfferTypeIdInPreapprovedCreditOffersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('preapproved_credit_offers', function (Blueprint $table) {
            $table->integer('credit_offer_type_id')->nullable();
            $table->foreign('credit_offer_type_id')->references('id')->on('preapproved_credit_offer_types');
        });

        DB::transaction(function () {
            DB::table('preapproved_credit_offers')->orderBy('id')->chunkById(1000, function ($items) {
                foreach ($items as $item) {
                    $type = DB::table('preapproved_credit_offer_types')
                        ->where('name', $item->type)
                        ->first();

                    if ($type) {
                        DB::table('preapproved_credit_offers')
                            ->where('id', $item->id)
                            ->update(['credit_offer_type_id' => $type->id]);
                    }
                }
            });

            Schema::table('preapproved_credit_offers', function (Blueprint $table) {
                $table->dropColumn('type');
            });
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('preapproved_credit_offers', function (Blueprint $table) {
            $table->dropForeign('preapproved_credit_offers_credit_offer_type_id_foreign');
            $table->dropColumn('credit_offer_type_id');
            $table->string('type')->nullable();
        });
    }
}
