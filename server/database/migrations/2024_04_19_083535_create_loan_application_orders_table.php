<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLoanApplicationOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('loan_application_orders', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('loan_security_id')->nullable();
            $table->integer('merchant_id')->nullable();
            $table->integer('vendor_id')->nullable();
            $table->integer('agent_id')->nullable();
            $table->string('ssn')->nullable();
            $table->string('document_number');
            $table->float('amount')->nullable();
            $table->string('phone_number')->nullable();
            $table->string('status');
            $table->dateTime('expiration')->nullable();
            $table->string('order_id')->nullable();
            $table->timestamps();

            $table->foreign('loan_security_id')->references('id')->on('loan_securities');
            $table->foreign('merchant_id')->references('id')->on('merchants');
            $table->foreign('vendor_id')->references('id')->on('vendors')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('loan_application_orders');
    }
}
