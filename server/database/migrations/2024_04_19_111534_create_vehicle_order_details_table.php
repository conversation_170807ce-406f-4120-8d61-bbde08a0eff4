<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateVehicleOrderDetailsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('vehicle_order_details', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('loan_application_order_id')->nullable();
            $table->string('police_code')->nullable();
            $table->string('vin')->nullable();
            $table->integer('released')->nullable();
            $table->timestamps();

            $table->foreign('loan_application_order_id')->references('id')->on('loan_application_orders')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('vehicle_order_details');
    }
}
