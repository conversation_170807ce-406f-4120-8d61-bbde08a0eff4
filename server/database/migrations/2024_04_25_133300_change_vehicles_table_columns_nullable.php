<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ChangeVehiclesTableColumnsNullable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('vehicles', function (Blueprint $table) {
            $table->string('number')->nullable()->change();
            $table->string('owner_cert_id')->nullable()->change();
            $table->string('cert_num')->nullable()->change();
            $table->string('vehicle_group')->nullable()->change();
            $table->string('fuel_type')->nullable()->change();
            $table->string('color')->nullable()->change();
        });

        Schema::table('merchants', function (Blueprint $table) {
            $table->string('state_reg_num')->nullable();
            $table->string('owner')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('vehicles', function (Blueprint $table) {
            $table->string('number')->nullable(false)->change();
            $table->string('owner_cert_id')->nullable(false)->change();
            $table->string('cert_num')->nullable(false)->change();
            $table->string('vehicle_group')->nullable(false)->change();
            $table->string('fuel_type')->nullable(false)->change();
            $table->string('color')->nullable(false)->change();
        });

        Schema::table('merchants', function (Blueprint $table) {
            $table->dropColumn('state_reg_num');
            $table->dropColumn('owner');
        });
    }
}
