<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddSessionIdAndIdentifierInEasypayWalletPaymentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('easypay_wallet_payments', function (Blueprint $table) {
            $table->string('session_id')->nullable();
            $table->string('identifier')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('easypay_wallet_payments', function (Blueprint $table) {
            $table->dropColumn('session_id');
            $table->dropColumn('identifier');
        });
    }
}
