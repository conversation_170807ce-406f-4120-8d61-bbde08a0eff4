<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTelcellWalletPaymentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('telcell_wallet_payments', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('wallet_id')->nullable();
            $table->date('paid')->nullable();
            $table->date('withdrawn')->nullable();
            $table->string('session_id')->nullable();
            $table->string('identifier')->nullable();
            $table->string('payment_status')->nullable();
            $table->integer('withdraw_operator_id')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('telcell_wallet_payments');
    }
}
