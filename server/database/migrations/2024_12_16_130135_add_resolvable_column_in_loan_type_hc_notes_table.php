<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddResolvableColumnInLoanTypeHcNotesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('loan_type_hc_notes', function (Blueprint $table) {
            $table->boolean('resolvable')->default(true);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('loan_type_hc_notes', function (Blueprint $table) {
            $table->dropColumn('resolvable');
        });
    }
}
