<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class RenameInternalClientCodesToInternalClientsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::rename('internal_client_codes', 'internal_clients');

        Schema::table('internal_clients', function (Blueprint $table) {
            $table->text('hash_details')->nullable();
        });

        Schema::dropIfExists('citizen_details');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::rename('internal_clients', 'internal_client_codes');

        Schema::table('internal_client_codes', function (Blueprint $table) {
            $table->dropColumn('hash_details');
        });
    }
}
