<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCitizenIdInMortgagesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('mortgages', function (Blueprint $table) {
            $table->unsignedInteger('citizen_id')->nullable();

            $table->foreign('citizen_id')
                ->references('id')
                ->on('citizens');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('mortgages', function (Blueprint $table) {
            $table->dropColumn('citizen_id');
        });
    }
}
