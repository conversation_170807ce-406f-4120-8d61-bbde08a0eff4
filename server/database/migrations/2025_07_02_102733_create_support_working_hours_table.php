<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateSupportWorkingHoursTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('support_working_hours', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedBigInteger('support_work_schedule_id');
            $table->tinyInteger('weekday');
            $table->time('from');
            $table->time('to');
            $table->timestamps();

            $table->foreign('support_work_schedule_id')
                ->references('id')
                ->on('support_work_schedules')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('support_working_hours');
    }
}
