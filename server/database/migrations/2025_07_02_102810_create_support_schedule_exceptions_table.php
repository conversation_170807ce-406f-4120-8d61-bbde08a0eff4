<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateSupportScheduleExceptionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('support_schedule_exceptions', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedBigInteger('support_work_schedule_id');
            $table->string('name');
            $table->date('date');
            $table->boolean('is_working');
            $table->time('from')->nullable();
            $table->time('to')->nullable();
            $table->string('message')->nullable();
            $table->timestamps();

            $table->foreign('support_work_schedule_id')
                ->references('id')
                ->on('support_work_schedules')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('support_schedule_exceptions');
    }
}
