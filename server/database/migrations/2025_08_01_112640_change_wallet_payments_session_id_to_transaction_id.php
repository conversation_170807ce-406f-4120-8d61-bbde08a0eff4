<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ChangeWalletPaymentsSessionIdToTransactionId extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('telcell_wallet_payments', function (Blueprint $table) {
            $table->renameColumn('session_id', 'transaction_id');
            $table->string('wallet_id')->change();
        });

        Schema::table('easypay_wallet_payments', function (Blueprint $table) {
            $table->renameColumn('session_id', 'transaction_id');
        });

        Schema::table('telcell_wallet_payments', function (Blueprint $table) {
            $table->dateTime('paid')->nullable()->change();
            $table->dateTime('withdrawn')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('telcell_wallet_payments', function (Blueprint $table) {
            $table->renameColumn('transaction_id', 'session_id');
            $table->integer('wallet_id')->change();
        });

        Schema::table('easypay_wallet_payments', function (Blueprint $table) {
            $table->renameColumn('transaction_id', 'session_id');
        });

        Schema::table('telcell_wallet_payments', function (Blueprint $table) {
            $table->date('paid')->nullable()->change();
            $table->date('withdrawn')->nullable()->change();
        });
    }
}
