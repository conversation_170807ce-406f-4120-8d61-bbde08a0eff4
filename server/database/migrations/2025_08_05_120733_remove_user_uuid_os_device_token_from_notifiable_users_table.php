<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class RemoveUserUuidOsDeviceTokenFromNotifiableUsersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('notifiable_users', function (Blueprint $table) {
            $table->dropColumn('user_uuid');
            $table->dropColumn('device_os');
            $table->dropColumn('device_token');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('notifiable_users', function (Blueprint $table) {
            $table->string('user_uuid')->nullable();
            $table->string('device_os')->nullable();
            $table->string('device_token')->nullable();
        });
    }
}
