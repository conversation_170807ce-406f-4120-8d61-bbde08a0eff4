<template>
  <card class="flex flex-col items-center justify-center">
    <div class="px-3 py-3">
      <h1 class="text-center text-2xl font-light">
        <a :href="card.url" class="no-underline font-normal text-90 loan-card">
          <div v-if="card.type === 'paid-loan'">
            <svg
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              x="0px"
              y="0px"
              viewBox="0 0 1000 1000"
              enable-background="new 0 0 1000 1000"
              xml:space="preserve"
            >
              <metadata>
                Svg Vector Icons : http://www.onlinewebfonts.com/icon
              </metadata>
              <g>
                <g
                  transform="translate(0.000000,512.000000) scale(0.100000,-0.100000)"
                >
                  <path
                    d="M4868.4,5011.4c-193-25.3-405.5-134.5-553.6-280.7c-161.8-163.8-249.5-350.9-284.6-606.2l-11.7-93.6h-302.1c-214.4,0-319.7-7.8-366.5-27.3c-87.7-37-216.4-154-245.6-226.1l-23.4-56.5l-787.5-5.8l-785.6-5.8l-44.8-44.8l-44.8-44.8V-542.2v-4163.7l46.8-37c44.8-37,128.7-37,3538-37h3495.1l40.9,44.8l42.9,44.8v4148.1c0,2666.7-5.8,4159.9-19.5,4183.3c-37,70.2-118.9,78-888.9,78H6925l-39,76c-37.1,74.1-109.2,142.3-214.4,198.8c-33.1,19.5-150.1,31.2-370.4,37l-321.6,9.7l-13.6,117C5907.4,4691.7,5404.5,5083.6,4868.4,5011.4z M5283.6,4637.2c200.8-103.3,343.1-300.2,368.4-504.9l11.7-101.4h-658.9H4346l11.7,105.3c21.4,208.6,163.8,399.6,374.3,502.9c107.2,52.6,130.6,56.5,292.4,48.7C5158.9,4683.9,5219.3,4670.3,5283.6,4637.2z M6583.8,3670.3l48.7-46.8v-450.3v-448.3l-1631.6,3.9l-1633.5,5.8v444.4v446.4l54.6,46.8l54.6,46.8h1530.2H6537L6583.8,3670.3z M3045.8,3222v-165.7h-438.6h-440.5l-46.8-48.7l-48.7-46.8V-528.5c0-1918.1,5.8-3502.9,11.7-3520.5c35.1-91.6-62.4-87.7,2931.8-87.7h2810.9l56.5,56.5l56.5,56.5V-550c0,3705.7,3.9,3559.5-87.7,3594.6c-17.5,5.8-224.2,11.7-460,11.7h-426.9V3222v165.7h653h653V-540.2v-3927.9H5004.9H1739.8v3927.9v3927.9h653h653V3222z M3045.8,2627.4c0-99.4,7.8-126.7,48.7-165.7l46.8-48.7h1851.9H6845l52.6,44.8c44.9,39,54.6,66.3,62.4,167.6l7.8,118.9l325.5-3.9l323.6-5.8V-540.2v-3274.9H5004.9H2392.8l-5.8,3280.7l-3.9,3278.8h331.4h331.4V2627.4z"
                  />
                  <path
                    d="M4880.1,4467.6c-52.6-68.2-50.7-130.6,5.8-194.9c97.5-115,265.1-48.7,265.1,101.4c0,91.6-56.5,144.3-154,144.3C4936.6,4518.3,4911.3,4506.6,4880.1,4467.6z"
                  />
                  <path
                    d="M4474.7,760c-56.5-7.8-122.8-91.6-122.8-154c0-27.3,25.3-72.1,56.5-105.3l56.5-56.5h524.4c382.1,0,536.1,7.8,571.1,25.3c101.4,50.7,117,200.8,27.3,259.3c-37,25.3-134.5,31.2-557.5,33.1C4747.6,763.9,4498.1,763.9,4474.7,760z"
                  />
                  <path
                    d="M3420.1-594.8c-58.5-62.4-56.5-157.9,5.8-218.3l46.8-48.7h1510.7c1066.3,0,1524.4,5.8,1557.5,21.4c95.5,42.9,118.9,167.7,42.9,241.7L6537-550H4999H3461L3420.1-594.8z"
                  />
                  <path
                    d="M3425.9-1904.8c-31.2-29.2-48.7-70.2-48.7-107.2c0-37.1,17.5-78,48.7-107.2l46.8-48.7h1520.5c836.3,0,1534.1,5.8,1551.7,11.7c48.7,19.5,91.6,120.8,78,185.2c-27.3,120.9,50.7,115-1623.8,115H3472.7L3425.9-1904.8z"
                  />
                </g>
              </g>
            </svg>
          </div>
          <div v-if="card.type === 'unpaid-loan'">
            <svg
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              x="0px"
              y="0px"
              viewBox="0 0 1000 1000"
              enable-background="new 0 0 1000 1000"
              xml:space="preserve"
            >
              <metadata>
                Svg Vector Icons : http://www.onlinewebfonts.com/icon
              </metadata>
              <g>
                <g
                  transform="translate(0.000000,512.000000) scale(0.100000,-0.100000)"
                >
                  <path
                    d="M4868.4,5011.4c-193-25.3-405.5-134.5-553.6-280.7c-161.8-163.8-249.5-350.9-284.6-606.2l-11.7-93.6h-302.1c-214.4,0-319.7-7.8-366.5-27.3c-87.7-37-216.4-154-245.6-226.1l-23.4-56.5l-787.5-5.8l-785.6-5.8l-44.8-44.8l-44.8-44.8V-542.2v-4163.7l46.8-37c44.8-37,128.7-37,3538-37h3495.1l40.9,44.8l42.9,44.8v4148.1c0,2666.7-5.8,4159.9-19.5,4183.3c-37,70.2-118.9,78-888.9,78H6925l-39,76c-37.1,74.1-109.2,142.3-214.4,198.8c-33.1,19.5-150.1,31.2-370.4,37l-321.6,9.7l-13.6,117C5907.4,4691.7,5404.5,5083.6,4868.4,5011.4z M5283.6,4637.2c200.8-103.3,343.1-300.2,368.4-504.9l11.7-101.4h-658.9H4346l11.7,105.3c21.4,208.6,163.8,399.6,374.3,502.9c107.2,52.6,130.6,56.5,292.4,48.7C5158.9,4683.9,5219.3,4670.3,5283.6,4637.2z M6583.8,3670.3l48.7-46.8v-450.3v-448.3l-1631.6,3.9l-1633.5,5.8v444.4v446.4l54.6,46.8l54.6,46.8h1530.2H6537L6583.8,3670.3z M3045.8,3222v-165.7h-438.6h-440.5l-46.8-48.7l-48.7-46.8V-528.5c0-1918.1,5.8-3502.9,11.7-3520.5c35.1-91.6-62.4-87.7,2931.8-87.7h2810.9l56.5,56.5l56.5,56.5V-550c0,3705.7,3.9,3559.5-87.7,3594.6c-17.5,5.8-224.2,11.7-460,11.7h-426.9V3222v165.7h653h653V-540.2v-3927.9H5004.9H1739.8v3927.9v3927.9h653h653V3222z M3049.7,2615.7c13.6-224.2-191-202.7,1947.4-202.7h1848l52.6,44.8c44.9,39,54.6,66.3,62.4,167.6l7.8,118.9l325.5-3.9l323.6-5.8V-540.2v-3274.9H5004.9H2392.8l-5.8,3280.7l-3.9,3278.8h329.4h329.4L3049.7,2615.7z"
                  />
                  <path
                    d="M4891.8,4483.2c-97.5-107.2-29.2-265.1,113.1-265.1c52.6,0,83.8,11.7,109.2,44.8c48.7,60.4,46.8,167.6-1.9,216.4C5061.4,4530,4936.6,4531.9,4891.8,4483.2z"
                  />
                  <path
                    d="M6365.5,1044.6c-23.4-25.3-446.4-655-941.5-1399.6c-495.1-744.6-908.4-1362.6-916.2-1370.4c-7.8-9.7-196.9,196.9-419.1,458.1c-222.2,259.3-424.9,493.2-452.2,518.5c-126.7,117-321.6-37-239.8-191c44.8-85.8,1019.5-1200.8,1062.4-1216.4c107.2-40.9,138.4-7.8,477.6,495.1C5901.6-222.5,6632.6,900.3,6632.6,943.2C6632.6,1066,6449.3,1134.2,6365.5,1044.6z"
                  />
                </g>
              </g>
            </svg>
          </div>
          <div v-if="card.type === 'users'">
            <svg
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              x="0px"
              y="0px"
              viewBox="0 0 1000 1000"
              enable-background="new 0 0 1000 1000"
              xml:space="preserve"
            >
              <metadata>
                Svg Vector Icons : http://www.onlinewebfonts.com/icon
              </metadata>
              <g>
                <path
                  d="M961.2,990H38.8l1.3-22c8.3-138.7,120-182.6,209.7-217.9c55-21.6,102.6-40.3,116.9-73.9c2.3-26.6,2.1-47.1,1.9-70.7c0-3.8-0.1-7.7-0.1-11.7c-21.3-20.6-43.5-66.5-51.3-107c-7.7-3.7-14.7-9.5-20.6-17c-11.2-14.3-18.6-35.1-21.8-61.7c-3.1-25.6,4.8-44.9,15.2-57.2c-28.4-117.1-20.3-206.1,24.1-264.6c37.3-49.1,98.9-74.8,183.2-76.3l0.4,0c55.5,0,96.9,15,118.1,42.4c39.3,7.6,69.2,27.4,88.8,59c21.2,34.1,29.5,80.8,24.9,138.8c-3.4,42.3-12.8,79.7-19.3,101.4c10.2,12.3,17.7,31.4,14.7,56.5c-3.2,26.6-10.5,47.3-21.8,61.7c-5.9,7.5-12.9,13.3-20.5,17c-5,27.2-15.3,51.2-21.6,64.1c-5.8,11.9-16.1,30.8-28.5,42.9c0,4.1-0.1,8-0.1,11.9c-0.2,23.6-0.4,44,1.9,70.5c14.3,33.6,61.8,52.3,116.5,73.9c89.4,35.3,200.6,79.2,208.9,217.9L961.2,990z M84.2,948.4h831.5c-16.5-95-99.6-127.8-180.1-159.6c-62.6-24.7-121.7-48-141.2-100.5l-1-2.6l-0.3-2.8c-2.7-30-2.5-53.1-2.3-77.6c0.1-6.9,0.1-14.1,0.1-21.5v-12.6l11-5.8c9.8-6.5,36.8-54.4,41.4-96.9l1.8-17.1l17.2-1.4c4.4-0.4,16.9-9.5,21.3-46.9c2.4-20.1-7.8-27.4-7.9-27.4l-14.1-8.6l5.1-15.7c17.9-53.8,38.5-160.2,2.5-218c-14.6-23.4-37-36.9-68.6-41.3l-10.1-1.4l-5.1-8.9c-10.7-18.8-43.5-30.1-87.6-30.1c-70.8,1.3-121.5,21.5-150.6,59.9c-37.3,49.1-42.1,132.6-13.9,241.2l3.9,15l-13.2,8c-0.1,0.1-10.4,7.4-8,27.5c4.5,37.4,16.9,46.5,21.3,46.9l17.2,1.4l1.8,17.1c2,18.5,9.1,41.3,19.4,62.4c10.6,21.5,20.1,32.4,23.1,34.4l11,5.8v12.6c0,7.4,0.1,14.5,0.1,21.3c0.2,24.5,0.4,47.7-2.3,77.8l-0.3,2.8l-1,2.6c-19.5,52.5-78.8,75.8-141.7,100.5C184.2,820.6,100.8,853.4,84.2,948.4z"
                />
              </g>
            </svg>
          </div>
          <div class="mt-3">{{ this.card.title }}</div>
        </a>
      </h1>
    </div>
  </card>
</template>

<script>
export default {
  props: ['card'],

  mounted() {},
};
</script>
