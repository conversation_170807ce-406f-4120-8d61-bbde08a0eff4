<?php

namespace Globalcredit\Home;

use Laravel\Nova\Card;

class Home extends Card
{
    /**
     * The width of the card (1/3, 1/2, or full).
     *
     * @var string
     */
    public $width = '1/3';

    public function title($title)
    {
        return $this->withMeta(['title' => $title]);
    }

    public function url($url)
    {
        return $this->withMeta(['url' => $url]);
    }

    public function type($type)
    {
        return $this->withMeta(['type' => $type]);
    }

    /**
     * Get the component name for the element.
     *
     * @return string
     */
    public function component()
    {
        return 'home';
    }
}
