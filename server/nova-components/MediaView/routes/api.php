<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Tool API Routes
|--------------------------------------------------------------------------
|
| Here is where you may register API routes for your tool. These routes
| are loaded by the ServiceProvider of your tool. You're free to add
| as many additional routes to this file as your tool may require.
|
*/

Route::get('/loan/{id}/media', 'Globalcredit\MediaView\Http\Controllers\MediaViewController@getMedia');

Route::post('/loan/{id}/media', 'Globalcredit\MediaView\Http\Controllers\MediaViewController@storeMedia');

Route::post('/loan/{id}/media/remove', 'Globalcredit\MediaView\Http\Controllers\MediaViewController@removeMedia');
