<?php

namespace Globalcredit\MediaView\Services;

use App\Exceptions\InternalErrorException;
use App\Models\ArpiSolarMedia;
use App\Models\Loan;
use Carbon\Carbon;
use function Functional\reduce_left;
use Illuminate\Http\UploadedFile;
use Smalot\PdfParser\Parser;

class ArpiSolarMediaService implements IMediaService
{
    public function getMedia($id, $document_type = null)
    {
        $loan = Loan::find($id);

        $media = $loan->solar_panel->arpi_solar_media()->where('document_type', $document_type)->get();

        return response()->json($media);
    }

    /**
     * @param $id
     * @param null $document_type
     *
     * @return \Illuminate\Http\JsonResponse
     *
     * @throws \Exception
     */
    public function storeMedia($id, UploadedFile $file, $document_type = null)
    {
        $loan = Loan::find($id);
        $path = "/{$loan->public_id}/{$file->hashName()}";

        if ($document_type === 'report') {
            $this->storeEvaluationReportData($loan, $file);
        }

        $media = $this->persistArpiSolarMedia($loan, $path, $file->getMimeType(), $file, $document_type);

        return response()->json($media);
    }

    public function removeMedia($id, $file)
    {
        $loan = Loan::find($id);

        $deleted = $loan->solar_panel->arpi_solar_media()->where('id', $file['key'])->delete();

        return response()->json(['deleted' => $deleted]);
    }

    private function persistArpiSolarMedia($loan, $path, $type, $content, $document_type)
    {
        $media = new ArpiSolarMedia([
            'path' => $path,
            'type' => $type,
            'document_type' => $document_type,
        ]);

        $this->storeArpiSolarMedia($media->path, $content);
        $arpi_solar_media = $loan->solar_panel->arpi_solar_media()->save($media);

        return $arpi_solar_media;
    }

    private function storeArpiSolarMedia($path, $file)
    {
        $awsService = resolve('App\Interfaces\IAWSService');
        $awsService->storeMortgageMedia($path, $file);
    }

    /**
     * @param $loan
     * @param $file
     *
     * @return mixed
     *
     * @throws \Exception
     */
    private function storeEvaluationReportData($loan, $file)
    {
        $parser = new Parser();

        $pdf = $parser->parseFile($file);

        $pages = $pdf->getPages();

        // Iterating over each page and return array of lines
        $context = reduce_left($pages, function ($value, $index, $collection, $reduction) {
            // Replacing with space, cause column separator doesn't have space,
            // to divide all words with space.
            $text = preg_replace('/(\s)?\t/', ' ', $value->getText());
            $text = preg_replace('/\s\n/', "\n", $text);

            $page = preg_split("/((\r?\n)|(\r\n?))/", $text);

            return array_merge($reduction, $page);
        }, []);

        if (!isset($context[0]) || $context[0] !== constants('OASL_XLSX_EXPORT_DATA.EVALUATION_REPORT')) {
            throw new InternalErrorException(lang('nova.exceptions.InvalidEvaluationReport'));
        }

        return $loan->solar_panel->evaluation_report()->updateOrCreate(
            ['solar_panel_id' => $loan->solar_panel->id],

            $this->getReportData($context)
        );
    }

    private function getReportData($context)
    {
        $bonusAmountData = explode(' ', $context[40]);

        $number = isset($bonusAmountData[2]) ?
            (float) str_replace(',', '', $bonusAmountData[2]) : 0;

        return [
            'evaluation_id' => $context[10],
            'evaluation_date' => Carbon::parse($context[13]),
            'bonus_amount' => $number,
        ];
    }
}
