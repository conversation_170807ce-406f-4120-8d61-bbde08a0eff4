<?php

namespace Globalcredit\MediaView\Services;

class MediaViewServiceFactory
{
    public static function build($type)
    {
        if ($type == constants('LOAN_TYPES.OASL')) {
            return resolve('Globalcredit\MediaView\Services\ArpiSolarMediaService');
        }

        if ($type == constants('LOAN_TYPES.OVL')) {
            return resolve('Globalcredit\MediaView\Services\VehicleMediaService');
        }

        if ($type == constants('LOAN_TYPES.REML')) {
            return resolve('Globalcredit\MediaView\Services\RealEstateMediaService');
        }
    }
}
