<?php

namespace Globalcredit\MediaView\Services;

use App\Models\Loan;
use App\Models\RealEstateDetail;
use App\Models\RealEstateMedia;

class RealEstateMediaService implements IMediaService
{
    const DIRECTORY_INDEX = 2;

    public function getMedia($id, $document_type = null)
    {
        $loan = Loan::find($id);
        $real_estate_details = RealEstateDetail::findByLoan($loan);

        $media = $real_estate_details->real_estate_media()
            ->where('document_type', $document_type)
            ->get()
            ->toArray();

        return response()->json($media);
    }

    public function storeMedia($id, $file, $document_type = null)
    {
        $loan = Loan::find($id);
        $real_estate_details = RealEstateDetail::findByLoan($loan);
        $path = $this->composePath($loan, $file, $real_estate_details);

        $media = $this->persistMedia($real_estate_details, $path, $file->getMimeType(), $file, $document_type);

        return response()->json($media);
    }

    public function removeMedia($id, $file)
    {
        RealEstateMedia::destroy($file['key']);

        return response()->json(['deleted' => true]);
    }

    private function persistMedia($real_estate_details, $path, $type, $content, $document_type)
    {
        $media = new RealEstateMedia([
            'path' => $path,
            'type' => $type,
            'name' => $content->getClientOriginalName(),
            'document_type' => $document_type,
        ]);

        $this->storeRealEstateMedia($media->path, $content);

        return $real_estate_details->real_estate_media()->save($media);
    }

    private function storeRealEstateMedia($path, $file)
    {
        $awsService = resolve('App\Interfaces\IAWSService');
        $awsService->storeMortgageMedia($path, $file);
    }

    private function composePath($loan, $file, $real_estate_details)
    {
        $real_estate_media = RealEstateMedia::getMediaByDetailId($real_estate_details->id, false);

        if (isset($real_estate_media)) {
            $directory = explode('/', trim($real_estate_media->path))[self::DIRECTORY_INDEX];

            return "/$directory/{$file->hashName()}";
        }

        return "/$loan->public_id/{$file->hashName()}";
    }
}
