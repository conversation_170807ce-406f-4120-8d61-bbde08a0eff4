<?php

namespace Globalcredit\MediaView\Services;

use App\Models\Loan;
use App\Models\VehicleMedia;

class VehicleMediaService implements IMediaService
{
    public function getMedia($id, $document_type = null)
    {
        $loan = Loan::find($id);

        $media = $loan->mortgage->vehicle_media;

        return response()->json($media);
    }

    public function storeMedia($id, $file, $document_type = null)
    {
        $loan = Loan::find($id);
        $path = "/{$loan->public_id}/{$file->hashName()}";

        $media = $this->persistVehicleMedia($loan, $path, $file->getMimeType(), $file);

        return response()->json($media);
    }

    public function removeMedia($id, $file)
    {
        $loan = Loan::find($id);

        $deleted = $loan->mortgage->vehicle_media()->where('id', $file['key'])->delete();

        return response()->json(['deleted' => $deleted]);
    }

    private function persistVehicleMedia($loan, $path, $type, $content)
    {
        $media = new VehicleMedia([
            'path' => $path,
            'type' => $type,
        ]);

        $this->storeVehicleMedia($media->path, $content);
        $vehicle_media = $loan->mortgage->vehicle_media()->save($media);

        return $vehicle_media;
    }

    private function storeVehicleMedia($path, $file)
    {
        $awsService = resolve('App\Interfaces\IAWSService');
        $awsService->storeMortgageMedia($path, $file);
    }
}
