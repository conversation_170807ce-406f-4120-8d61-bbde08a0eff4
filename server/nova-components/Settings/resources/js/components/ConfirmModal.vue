<template>
  <modal @modal-close="handleClose" class="flex confirm-modal">
    <form
      @submit.prevent="handleConfirm"
      class="bg-white rounded-lg shadow-lg overflow-hidden"
      style="width: 400px"
    >
      <div class="bg-30 px-6 py-3">
        <div class="details-section center">
          {{ __('Confirm the action') }}
        </div>

        <div class="mx-auto actions center btn-container">
          <button
            @click.prevent="handleClose"
            class="btn btn-default font-normal"
          >
            {{ __('Cancel') }}
          </button>
          <button class="btn btn-default btn-primary w-24 ml-4 font-normal">
            {{ __('Accept') }}
          </button>
        </div>
      </div>
    </form>
  </modal>
</template>

<script>
export default {
  methods: {
    handleClose() {
      this.$emit('close');
    },

    handleConfirm() {
      this.$emit('confirm');
    },
  },
};
</script>

<style>
.btn-container {
  justify-content: center !important;
}
.btn-primary {
  background-color: #0075ff;
  color: white;
  padding: 1px 16px;
  border-radius: 4px;
}
</style>
