<template>
  <div>
    <div class="card relative">
      <div class="py-3 flex items-center border-b border-50 justify-between">
        <!-- Section Switcher -->
        <div class="flex items-center">
          <div class="px-3">
            <div class="dropdown relative" dusk="select-all-dropdown">
              <heading class="mb-6">{{ __('Settings') }}</heading>
            </div>
          </div>
        </div>
        <div class="flex space-x-4 px-3">
          <button
            :class="getSectionClass('loan')"
            @click="switchSection('loan')"
          >
            {{ __('Loan Settings') }}
          </button>
          <button
            :class="getSectionClass('general')"
            @click="switchSection('general')"
          >
            {{ __('General Settings') }}
          </button>
        </div>
      </div>

      <!-- Conditional Section Rendering -->
      <div
        v-if="currentSection === 'loan'"
        class="overflow-hidden overflow-x-auto relative"
      >
        <table data-testid="resource-table" class="table w-full">
          <thead>
            <tr>
              <th class="w-16">
                &nbsp;
              </th>
              <th class="text-left"><span> # </span></th>
              <th class="text-left"><span> Անվանում </span></th>
              <th class="text-left"><span> Նկարագրություն </span></th>
              <th class="text-left"><span> Կարգավիճակ </span></th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-if="loanTypes.length"
              v-for="(item, key) in loanTypes"
              :key="item.name"
            >
              <td class="w-16">
                <div
                  class="flex items-center"
                  data-testid="arpi_solar_loans-items-0-checkbox"
                  dusk="3-checkbox"
                >
                  <div
                    tabindex="0"
                    role="checkbox"
                    class="checkbox select-none rounded"
                  ></div>
                </div>
              </td>
              <td>
                <span class="whitespace-no-wrap text-left">{{ ++key }}</span>
              </td>
              <td>
                <span class="whitespace-no-wrap text-left">{{
                  item.name
                }}</span>
              </td>
              <td>
                <span class="whitespace-no-wrap text-left">
                  {{ item.description ? item.description : item.name }}
                </span>
              </td>
              <td>
                <toggle-button
                  color="#0075ff"
                  :value="!item.disabled"
                  :sync="true"
                  :tag="item.name"
                  :labels="true"
                  @change="openConfirmModal($event)"
                />
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div v-else-if="currentSection === 'general'" class="p-4">
        <div class="overflow-hidden overflow-x-auto relative">
          <table data-testid="resource-table" class="table w-full">
            <thead>
              <tr>
                <th class="w-16">
                  &nbsp;
                </th>
                <th class="text-left"><span> # </span></th>
                <th class="text-left"><span> Անվանում </span></th>
                <th class="text-left"><span> Նկարագրություն </span></th>
                <th class="text-left"><span> Կարգավիճակ </span></th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-if="generalSettings.length"
                v-for="(item, key) in generalSettings"
                :key="item.name"
              >
                <td class="w-16">
                  <div
                    class="flex items-center"
                    data-testid="arpi_solar_loans-items-0-checkbox"
                    dusk="3-checkbox"
                  >
                    <div
                      tabindex="0"
                      role="checkbox"
                      class="checkbox select-none rounded"
                    ></div>
                  </div>
                </td>
                <td>
                  <span class="whitespace-no-wrap text-left">{{ ++key }}</span>
                </td>
                <td>
                  <span class="whitespace-no-wrap text-left">{{
                    item.name
                  }}</span>
                </td>
                <td>
                  <span class="whitespace-no-wrap text-left">
                    {{ item.description ? item.description : item.name }}
                  </span>
                </td>
                <td>
                  <toggle-button
                    color="#0075ff"
                    :value="!item.disabled"
                    :sync="true"
                    :tag="item.name"
                    :labels="true"
                    @change="openConfirmModal($event)"
                  />
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <div class="block mx-auto loading">
      <loading v-if="loading" :large="true" />
    </div>
    <confirm-modal
      v-if="showModal"
      @close="hideModal()"
      @confirm="handleConfirm"
    />
  </div>
</template>

<script>
import { ToggleButton } from 'vue-js-toggle-button';

export default {
  components: { ToggleButton },

  data() {
    return {
      switcher: null,
      loading: true,
      statusMessage: '',
      showModal: false,
      requestStatus: true,
      currentSection: 'loan',
      loanTypes: [],
      generalSettings: [],
      statusMessages: {
        failed: this.__('Action is failed'),
        success: this.__('The action ran successfully!'),
      },
    };
  },

  mounted() {
    this.getSettings();
  },

  methods: {
    async getSettings() {
      try {
        const response = await Nova.request().get(
          '/nova-vendor/settings/settings'
        );

        this.loanTypes = response.data.loan_types;
        this.generalSettings = response.data.general_settings;
      } catch (error) {
        this.loading = false;

        const message =
          this.__(error.response.data.message) || this.statusMessages.failed;
        this.statusMessage = this.statusMessages.failed;
        this.showToast('error', message);
      } finally {
        this.loading = false;
      }
    },

    async handleConfirm() {
      try {
        this.loading = true;
        const payload = this.composePayload();

        await Nova.request().put('/nova-vendor/settings/settings', payload);

        this.loanTypes = this.loanTypes.map(el => {
          if (el.name == payload.name) {
            el.disabled = payload.disabled;
          }
          return el;
        });
        this.generalSettings = this.generalSettings.map(el => {
          if (el.name == payload.name) {
            el.disabled = payload.disabled;
          }
          return el;
        });

        const message = this.__(this.statusMessages.success);
        this.showToast('success', message);
      } catch (error) {
        const message =
          this.__(error.response.data.message) || this.statusMessages.failed;
        this.statusMessage = this.statusMessages.failed;
        this.requestStatus = false;
        this.showToast('error', message);
      } finally {
        this.hideModal();
        this.loading = false;
      }
    },

    showToast(type, message) {
      this.$toasted.show(this.__(`${message}`), { type: type });
    },

    composePayload() {
      return {
        sectionId: this.currentSection === 'loan' ? 0 : 1,
        name: this.switcher.tag,
        disabled: !this.switcher.value,
      };
    },

    openConfirmModal(e) {
      this.showModal = true;
      this.switcher = e;
    },

    hideModal() {
      this.showModal = false;
    },

    switchSection(section) {
      this.currentSection = section;
    },

    getSectionClass(section) {
      return this.currentSection === section ? 'btn-primary' : 'btn-secondary';
    },
  },
};
</script>

<style>
.btn-primary {
  background-color: #0075ff;
  color: white;
  padding: 0px 16px;
  border-radius: 4px;
}

.btn-secondary {
  background-color: #f3f4f6;
  color: #374151;
  padding: 8px 16px;
  border-radius: 4px;
}
</style>
