{"private": true, "scripts": {"dev": "npm run development", "development": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "watch": "npm run development -- --watch", "watch-poll": "npm run watch -- --watch-poll", "hot": "cross-env NODE_ENV=development node_modules/webpack-dev-server/bin/webpack-dev-server.js --inline --hot --config=node_modules/laravel-mix/setup/webpack.config.js", "prod": "npm run production", "production": "cross-env NODE_ENV=production node_modules/webpack/bin/webpack.js --no-progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "build-acra-monitoring": "cd nova-components/AcraMonitoring && npm run dev", "build-acra-monitoring-prod": "cd nova-components/AcraMonitoring && npm run prod"}, "devDependencies": {"@babel/preset-react": "^7.16.7", "axios": "^0.18", "bootstrap": "^4.0.0", "browser-sync": "^2.27.7", "browser-sync-webpack-plugin": "^2.0.1", "cross-env": "^5.1", "eslint-config-prettier": "^6.10.0", "eslint-plugin-prettier": "^3.1.2", "husky": "^6.0.0", "jquery": "^3.2", "laravel-mix": "^4.0.7", "lint-staged": "7.3.0", "prettier": "^1.19.1", "react": "^16.5.2", "react-dom": "^16.5.2", "react-outside-click-handler": "^1.3.0", "react-router-dom": "4.3.1", "resolve-url-loader": "^2.3.1", "sass": "^1.15.2", "sass-loader": "^7.1.0", "vue-template-compiler": "^2.6.11"}, "dependencies": {"ao-components": "git+https://<EMAIL>/aobyte/ao-components.git", "bootstrap-sass": "^3.4.1", "effective-interest-rate": "^0.1.0", "formik": "1.4.3", "google-maps-react": "^2.0.2", "humps": "2.0.1", "i18next": "11.9.1", "i18next-browser-languagedetector": "2.2.3", "i18next-xhr-backend": "1.5.1", "jquery-input-lettering": "^0.1.2", "jsnlog": "^2.29.0", "laravel-mix-react-css-modules": "1", "laravel-mix-svg": "^0.4.1", "lodash": "^4.17.11", "moment": "^2.24.0", "pdfjs-dist": "^2.14.305", "popper.js": "^1.16.1", "prop-types": "^15.6.2", "query-string": "6.2.0", "react-albus": "2.0.0", "react-compound-slider": "^0.16.3", "react-device-detect": "^1.11.14", "react-google-recaptcha": "^1.0.5", "react-i18next": "8.0.7", "react-loading-skeleton": "^2.0.0", "react-number-format": "^4.4.1", "react-redux": "5.0.7", "react-show-more-text": "^1.4.6", "redux": "4.0.0", "redux-observable": "1.0.0", "rxjs": "6.3.3", "semantic-ui-css": "2.4.1", "semantic-ui-react": "0.84.0", "yup": "0.26.10"}, "lint-staged": {"linters": {"./**/*.{js,jsx,json,css,scss,vue}": ["prettier --config .prettierrc --write", "git add"]}, "ignore": ["./nova-components/**/dist/**/*"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}}