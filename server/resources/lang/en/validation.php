<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines contain the default error messages used by
    | the validator class. Some of these rules have multiple versions such
    | as the size rules. Feel free to tweak each of these messages here.
    |
    */

    'accepted' => 'The :attribute must be accepted.',
    'active_url' => 'The :attribute is not a valid URL.',
    'after' => 'The :attribute must be a date after :date.',
    'alpha' => 'The :attribute may only contain letters.',
    'alpha_dash' => 'The :attribute may only contain letters, numbers, and dashes.',
    'alpha_num' => 'The :attribute may only contain letters and numbers.',
    'array' => 'The :attribute must be an array.',
    'before' => 'The :attribute must be a date before :date.',
    'between' => [
        'numeric' => 'The :attribute must be between :min and :max.',
        'file' => 'The :attribute must be between :min and :max kilobytes.',
        'string' => 'The :attribute must be between :min and :max characters.',
        'array' => 'The :attribute must have between :min and :max items.',
    ],
    'boolean' => 'The :attribute field must be true or false.',
    'confirmed' => 'The :attribute confirmation does not match.',
    'date' => 'The :attribute is not a valid date.',
    'date_format' => 'The :attribute does not match the format :format.',
    'different' => 'The :attribute and :other must be different.',
    'digits' => 'The :attribute must be :digits digits.',
    'digits_between' => 'The :attribute must be between :min and :max digits.',
    'dimensions' => 'The :attribute has invalid image dimensions.',
    'distinct' => 'The :attribute field has a duplicate value.',
    'email' => 'The :attribute must be a valid email address.',
    'exists' => 'The selected :attribute is invalid.',
    'file' => 'The :attribute must be a file.',
    'filled' => 'The :attribute field is required.',
    'image' => 'The :attribute must be an image.',
    'in' => 'The selected :attribute is invalid.',
    'in_array' => 'The :attribute field does not exist in :other.',
    'integer' => 'The :attribute must be an integer.',
    'ip' => 'The :attribute must be a valid IP address.',
    'json' => 'The :attribute must be a valid JSON string.',
    'max' => [
        'numeric' => 'The :attribute may not be greater than :max.',
        'file' => 'The :attribute may not be greater than :max kilobytes.',
        'string' => 'The :attribute may not be greater than :max characters.',
        'array' => 'The :attribute may not have more than :max items.',
    ],
    'mimes' => 'The :attribute must be a file of type: :values.',
    'mimetypes' => 'The :attribute must be a file of type: :values.',
    'min' => [
        'numeric' => 'The :attribute must be at least :min.',
        'file' => 'The :attribute must be at least :min kilobytes.',
        'string' => 'The :attribute must be at least :min characters.',
        'array' => 'The :attribute must have at least :min items.',
    ],
    'not_in' => 'The selected :attribute is invalid.',
    'numeric' => 'The :attribute must be a number.',
    'present' => 'The :attribute field must be present.',
    'regex' => 'The :attribute format is invalid.',
    'required' => 'The :attribute field is required.',
    'required_if' => 'The :attribute field is required when :other is :value.',
    'required_unless' => 'The :attribute field is required unless :other is in :values.',
    'required_with' => 'The :attribute field is required when :values is present.',
    'required_with_all' => 'The :attribute field is required when :values is present.',
    'required_without' => 'The :attribute field is required when :values is not present.',
    'required_without_all' => 'The :attribute field is required when none of :values are present.',
    'same' => 'The :attribute and :other must match.',
    'size' => [
        'numeric' => 'The :attribute must be :size.',
        'file' => 'The :attribute must be :size kilobytes.',
        'string' => 'The :attribute must be :size characters.',
        'array' => 'The :attribute must contain :size items.',
    ],
    'string' => 'The :attribute must be a string.',
    'timezone' => 'The :attribute must be a valid zone.',
    'unique' => 'The :attribute has already been taken.',
    'uploaded' => 'The :attribute failed to upload.',
    'url' => 'The :attribute format is invalid.',

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | Here you may specify custom validation messages for attributes using the
    | convention "attribute.rule" to name the lines. This makes it quick to
    | specify a specific custom language line for a given attribute rule.
    |
    */

    'custom' => [
        'email' => [
            'email' => Config::get('error_codes.INVALID_EMAIL'),
            'required' => Config::get('error_codes.FIELD_REQUIRED'),
        ],
        'password' => [
            'required' => Config::get('error_codes.FIELD_REQUIRED'),
        ],
        'gcd' => [
            'required' => Config::get('error_codes.FIELD_REQUIRED'),
            'document_number' => Config::get('error_codes.INVALID_DOCUMENT_NUMBER'),
        ],
        'gcc' => [
            'boolean' => Config::get('error_codes.NOT_BOOLEAN'),
        ],
        'passport_number' => [
            'required' => Config::get('error_codes.FIELD_REQUIRED'),
            'passport' => Config::get('error_codes.INVALID_PASSPORT'),
        ],
        'soc_card_number' => [
            'required' => Config::get('error_codes.FIELD_REQUIRED'),
            'soc_card' => Config::get('error_codes.INVALID_SOC_CARD'),
        ],
        'loan_type' => [
            'required' => Config::get('error_codes.CRM.FIELD_REQUIRED'),
            'numeric' => Config::get('error_codes.CRM.NOT_NUMERIC'),
        ],
        'amount' => [
            'required' => Config::get('error_codes.FIELD_REQUIRED'),
            'numeric' => Config::get('error_codes.NOT_NUMERIC'),
        ],
        'monthly_payment' => [
            'required' => Config::get('error_codes.FIELD_REQUIRED'),
            'numeric' => Config::get('error_codes.NOT_NUMERIC'),
        ],
        'months' => [
            'required' => Config::get('error_codes.FIELD_REQUIRED'),
            'numeric' => Config::get('error_codes.NOT_NUMERIC'),
        ],
        'last_month_payment' => [
            'required' => Config::get('error_codes.FIELD_REQUIRED'),
            'numeric' => Config::get('error_codes.NOT_NUMERIC'),
        ],
        'total_amount' => [
            'required' => Config::get('error_codes.FIELD_REQUIRED'),
            'numeric' => Config::get('error_codes.NOT_NUMERIC'),
        ],
        'phone_number' => [
            'required' => Config::get('error_codes.FIELD_REQUIRED'),
            'arm_phone' => Config::get('error_codes.ARM_PHONE'),
        ],
        'notification_method' => [
            'required' => Config::get('error_codes.FIELD_REQUIRED'),
            'notification_method' => Config::get('error_codes.INVALID_NOTIFICATION_METHOD'),
        ],
        'citizen_image' => [
            'max' => Config::get('error_codes.LIVENESS_IMAGE_SIZE'),
        ],
        'checkup_date' => [
            'required' => Config::get('error_codes.FIELD_REQUIRED'),
            'vehicle_check_up_date' => Config::get('error_codes.INVALID_VEHICLE_CHECK_UP_DATE'),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Attributes
    |--------------------------------------------------------------------------
    |
    | The following language lines are used to swap attribute place-holders
    | with something more reader friendly such as E-Mail Address instead
    | of "email". This simply helps us make messages a little cleaner.
    |
    */

    'attributes' => [],
    'numeric_with_dot' => 'The value must be a number, not starting with 0, and float should be separated with dot.',
];
