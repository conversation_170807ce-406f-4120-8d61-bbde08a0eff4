<?php

return [
    // TODO: Make package specific
    'mimes' => 'Թույլատրելի տեսակներն են :values',
    'active_url' => ':attribute անվավեր URL:',
    'after' => ':attribute֊ը պետք է լինի ավելի ուշ, քան :date֊ը:',
    'after_or_equal' => ':attribute֊ը անվավեր է։',
    'alpha' => 'Դաշտը պետք է պարունակի միայն տառեր:',
    'alpha_dash' => 'Դաշտը պետք է պարունակի միայն տառեր, թվեր եւ գծիկներ:',
    'alpha_num' => 'Դաշտը պետք է պարունակի միայն տառեր եւ թվեր:',
    'before' => ':attribute֊ը պետք է լինի ավելի շուտ, քան :date֊ը:',
    'between' => [
        'numeric' => ':attribute դաշտը պետք է լինի :min - :max միջակայքում:',
        'string' => ':attribute դաշտը պետք է լինի :min - :max միջակայքում:',
        'array' => ':attribute դաշտը պետք է պարունակի :min - :max միավոր:',
    ],
    'date' => 'Անվավեր ամսաթիվ:',
    'date_format' => 'Սխալ ամսաթվի ֆորմատ:',
    'email' => 'Մուտքագրեք վավեր էլ. հասցե:',
    'integer' => 'Դաշտը պետք է պարունակի միայն թվեր:',
    'required' => 'Դաշտը պարտադիր է:',
    'required_if' => ':attribute դաշտը պարտադիր է, երբ :other-ը :value է:',
    'required_with' => ':attribute դաշտը պարտադիր է, երբ :values կան:',
    'required_with_all' => ':attribute դաշտը պարտադիր է, երբ :values կան:',
    'required_without' => ':attribute դաշտը պարտադիր է, երբ :values չկան:',
    'required_without_all' => ':attribute դաշտը պարտադիր է, երբ :values-ը բացակայում են:',
    'same' => ':attribute-ը եւ :other-ը պետք է համապատասխանեն:',
    'size' => [
        'numeric' => ':attribute-ը պետք է լինի :size նիշ:',
        'string' => ':attribute-ը պետք է լինի :size նիշ:',
        'array' => ':attribute-ը պետք է ունենա :size միավոր:',
    ],
    'max' => [
        'numeric' => 'Թույլատրելի նիշերի քանակ՝ :max',
        'string' => 'Թույլատրելի նիշերի քանակ՝ :max',
        'array' => 'Թույլատրելի միավորի քանակ՝ :max',
    ],
    'min' => [
        'numeric' => ':attribute-ը պետք է պարունակի :min և ավել նիշ։',
        'string' => ':attribute-ը պետք է պարունակի :min և ավել նիշ։',
        'array' => ':attribute-ը պետք է պարունակի :min և ավել միավոր։',
    ],
    'string' => ':attribute-ը պետք է լինի տեքստ:',
    'unique' => 'Նման արժեք արդեն գոյություն ունի:',
    'url' => 'Անվավեր ֆորմատ:',
    'digits' => ':attribute-ը պետք է պարունակի :digits թվանշան։',
    'numeric' => 'Դաշտը պետք է լինի թիվ:',
    'custom' => [
        'email' => [
            'email' => 'Սխալ ֆորմատի էլ․ հասցե',
            'required' => 'Այս դաշտը պարտադիր է',
        ],
        'password' => [
            'required' => 'Այս դաշտը պարտադիր է',
        ],
        'phone_number' => [
            'required' => 'Այս դաշտը պարտադիր է',
            'invalid' => 'Հեռախոսահամարը պետք է վավեր լինի',
        ],
        'amount' => [
            'required' => 'Դաշտը պարտադիր է',
        ],
        'days' => [
            'min' => 'Օրերի քանակը պետք է մեծ կամ հավասար լինի :min-ից',
        ],
    ],
    'dimensions' => 'Նկարի անթույլատրելի չափ',
    'numeric_with_dot' => 'Արժեքը չպետք է սկսվի զրոյով, եթե ամբողջ թիվ է, իսկ տասնորդականի դեպքում պետք է բաժանվի կետով',
];
