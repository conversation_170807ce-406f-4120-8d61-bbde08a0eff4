/**
 * These codes are generally using in the wallet blades logic(non-react part)
 */

import Log from './logs';
const axios = require('axios');

axios.interceptors.response.use(
  response => {
    return response;
  },
  error => {
    const errorData = error.response.data && error.response.data.error;
    if (errorData != undefined) Log.error(errorData);
    return Promise.reject(error);
  }
);

export const getLoan = company => {
  return (location.href = `${appUrl}/api/${company}/request/get-loan`);
};

export const approveLoan = (
  notification_method,
  dispute_solution_method,
  company
) => {
  return axios.post(
    `${appUrl}/api/${company}/request/approve-loan`,
    { amount, notification_method, dispute_solution_method },
    {}
  );
};

export const getVerificationCode = (apr, company) => {
  return axios.post(
    `${appUrl}/api/${company}/request/get-verification-code`,
    { submitted_apr: apr },
    {}
  );
};

export const verifyCode = (data, company) => {
  return axios.post(`${appUrl}/api/${company}/request/verify-code`, data, {});
};
