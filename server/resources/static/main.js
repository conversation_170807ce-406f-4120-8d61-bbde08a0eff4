/**
 * These codes are generally using in the wallet blades logic(non-react part)
 */

import { getLoan, approveLoan, getVerificationCode, verifyCode } from './api';
import $ from 'jquery';
import {
  VALIDATION_CODE_ERRORS,
  VALIDATION_MESSAGES,
  TERM_PDF,
} from './constants';
require('jquery-input-lettering/src/jquery.inputLettering');
const pdfjsLib = require('pdfjs-dist/legacy/build/pdf.js');
pdfjsLib.GlobalWorkerOptions.workerSrc = require('pdfjs-dist/build/pdf.worker.entry.js');

const $modal = $('.modal'),
  $spinner = $(
    '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>'
  ),
  $loanContentAgreementCheckbox = $('.loan-content .agreement-checkbox'),
  $loanContentSubmitButton = $('.loan-content .submit-button'),
  $agreementSubmitButton = $('.agreement-content .submit-button'),
  $contractContentGetCodeButton = $('.contract-content .get-code'),
  $contractAprCheckbox = $('.contract-content .apr-checkbox'),
  $contractInputBox = $('.contract-content .input-box'),
  $contractAprInput = $contractInputBox.find('input'),
  company = window.location.pathname.split('/')[2],
  confirmationCodeLength = 4,
  codeInputBlockClassList = document.querySelector('#codeInput > div')
    ?.classList,
  $confirmTermsContent = $('.confirm-terms-content'),
  $confirmTermsSubmitButton = $('.confirm-terms-content .submit-button'),
  $confirmTermsAgreementCheckboxOne = $(
    '.confirm-terms-content .agreement-checkbox-one'
  ),
  $confirmTermsAgreementCheckboxTwo = $(
    '.confirm-terms-content .agreement-checkbox-two'
  ),
  $confirmTermsModalButton = $('.confirm-terms-content .terms-modal-button'),
  $confirmTermsModal = $('.confirm-terms-content .modal'),
  $verificationCodeModal = $('.sms-code-modal'),
  $errorModal = $('.error-modal'),
  $loanConfirmationInterruptedCode = 4102;

$(window).on('unload', () => {
  $loanContentAgreementCheckbox.prop('checked', false);
  $confirmTermsAgreementCheckboxOne.prop('checked', false);
  $confirmTermsAgreementCheckboxTwo.prop('checked', false);
});

const allowSubmit = checkbox => {
  $loanContentSubmitButton.attr('disabled', !checkbox.checked);
};

const allowConfirmTermsSubmit = () => {
  const checked =
    $confirmTermsAgreementCheckboxOne.is(':checked') &&
    $confirmTermsAgreementCheckboxTwo.is(':checked');

  $confirmTermsSubmitButton.attr('disabled', !checked);
};

const allowGetCode = checkbox => {
  $contractInputBox.toggleClass('d-none');

  if (!checkbox.checked) {
    $contractInputBox.removeClass('error');
    $contractContentGetCodeButton.attr('disabled', true);
    $contractAprInput.val('');
    $contractAprInput.data('old-val', '');
  }
};

$modal.on('hidden.bs.modal', () => {
  $modal.find('.error-box').html('');
  $modal.find('input').val('');
});

$contractAprInput.on('keyup', function(e) {
  const $this = $(this);
  const oldVal = $this.data('old-val');

  $contractInputBox.removeClass('error');

  // Accept only integer or floating number with dot or comma
  const reg = new RegExp(/^(\d+|(\d+[.,]([\d]?)+))$/);

  if (e.target.value && e.target.value.length && !reg.test(e.target.value)) {
    $this.val(oldVal);
  } else {
    $this.data('old-val', $this.val());

    $contractContentGetCodeButton.attr(
      'disabled',
      parseFloat($this.val().replace(',', '.')) !== $this.data('apr')
    );
  }
});

$contractAprInput.on('focusout', function() {
  if (
    parseFloat($contractAprInput.val().replace(',', '.')) !==
    $contractAprInput.data('apr')
  ) {
    $contractInputBox.addClass('error');
  }
});

$loanContentAgreementCheckbox.on('change', function() {
  allowSubmit(this);
});

$([$confirmTermsAgreementCheckboxOne, $confirmTermsAgreementCheckboxTwo]).each(
  function() {
    $(this).on('change', function() {
      allowConfirmTermsSubmit();
    });
  }
);

$contractAprCheckbox.on('change', function() {
  allowGetCode(this);
});

$loanContentSubmitButton.on('click', function() {
  let notification_method = $(this)
    .closest('main')
    .find("select[name='notification_method']")
    .val();
  let dispute_solution_method = $(this)
    .closest('main')
    .find("select[name='dispute_solution_method']")
    .val();

  $loanContentSubmitButton.attr('disabled', true).prepend($spinner);

  approveLoan(notification_method, dispute_solution_method, company)
    .then(response => {
      if (
        response.status == 200 &&
        response.data.amount !== undefined &&
        response.data.amount > 0
      ) {
        location.href = `${appUrl}/api/${company}/request/agreement`;
      }
    })
    .finally(() => {
      $loanContentSubmitButton.attr('disabled', false);
      $spinner.remove();
    });
});

$confirmTermsSubmitButton.on('click', function() {
  $confirmTermsSubmitButton.attr('disabled', true).prepend($spinner);
  if (!sessionStorage.getItem('confirmTermsUrl')) {
    sessionStorage.setItem('confirmTermsUrl', location.href);
  }
  getLoan(company);
});

$agreementSubmitButton.on('click', function() {
  $(this)
    .attr('disabled', true)
    .prepend($spinner);

  setTimeout(() => {
    $(this).attr('disabled', false);
    $spinner.remove();
  }, 1000);
});

$errorModal.on('click', function() {
  $errorModal.modal('hide');
  const confirmTermsUrl = sessionStorage.getItem('confirmTermsUrl');
  if (confirmTermsUrl) {
    $errorModal.find('.error-message').html('');
    location.href = confirmTermsUrl;
  }
});
$confirmTermsModalButton.on('click', function() {
  $confirmTermsModal.modal('show');
  $('.modal-body').scrollTop();

  const loadingTask = pdfjsLib.getDocument(TERM_PDF);
  loadingTask.promise.then(function(pdf) {
    const totalPages = pdf.numPages;
    // Fetch the first page
    let pageNumber = 1;
    for (let i = 1; i <= totalPages; i += 1) {
      let id = 'the-canvas' + i;
      $('#canvas_div').append(
        "<canvas class='the-canvas' id='" + id + "'></canvas>"
      );
      let canvas = document.getElementById(id);

      renderPage(canvas, pdf, pageNumber++, function pageRenderingComplete() {
        if (pageNumber > pdf.numPages) {
          return;
        }
        // Continue rendering of the next page
        renderPage(canvas, pdf, pageNumber++, pageRenderingComplete);
      });
    }
  });

  function renderPage(canvas, pdf, pageNumber, callback) {
    pdf.getPage(pageNumber).then(function(page) {
      const scale = 2;
      const viewport = page.getViewport({ scale: scale });
      const pageDisplayWidth = viewport.width;
      const pageDisplayHeight = viewport.height;
      const context = canvas.getContext('2d');
      canvas.width = pageDisplayWidth;
      canvas.height = pageDisplayHeight;

      const renderContext = {
        canvasContext: context,
        viewport: viewport,
      };
      page.render(renderContext).promise.then(callback);
    });
  }
});

$confirmTermsContent.on('hide.bs.modal', function() {
  $('.modal-body .the-canvas').remove();
});

$contractContentGetCodeButton.on('click', function() {
  $contractContentGetCodeButton.attr('disabled', true);

  codeInputBlockClassList?.remove('invalid');
  const $errorBox = $contractContentGetCodeButton.siblings('.error-box');

  $errorBox.html('');

  getVerificationCode($contractAprInput.val(), company)
    .then(response => {
      $verificationCodeModal.modal('show');
      if (response.status == 200 && !response.data.sent) {
        $verificationCodeModal
          .find('.error-box')
          .html(VALIDATION_MESSAGES['last_code_text']);
      }
    })
    .catch(error => {
      requestFailed(error, $errorBox);
    })
    .finally(() => {
      $contractContentGetCodeButton.attr('disabled', false);
    });
});

$('.verify-code-form').on('submit', function(e) {
  e.preventDefault();

  const $this = $(this);
  let code = $this.find("input[name='code']").val();
  let $errorBox = $this.find('.error-box');
  let $submitButton = $this.find('.submit-button');

  codeInputBlockClassList?.remove('invalid');
  $errorBox.html('');
  $submitButton.attr('disabled', true);
  if (code.length !== confirmationCodeLength) {
    codeInputBlockClassList.add('invalid');
    $submitButton.attr('disabled', false);
    return; // Stop fom submission
  }
  verifyCode(`code=${code}`, company)
    .then(() => {
      $verificationCodeModal.modal('show');
      location.href = `${appUrl}/api/${company}/request/confirmation/success`;
    })
    .catch(error => {
      requestFailed(error, $errorBox);
    })
    .finally(() => {
      $submitButton.attr('disabled', false);
    });
});

const requestFailed = (error, $errorBox) => {
  const response = error.response,
    errorCode =
      response.data.error &&
      (response.data.error.errors || response.data.error.code);

  if (errorCode !== undefined) {
    let message = '';

    if (errorCode instanceof Object && response.status === 422) {
      message = 'hasError';
    } else {
      message = VALIDATION_CODE_ERRORS.hasOwnProperty(errorCode)
        ? VALIDATION_CODE_ERRORS[errorCode]
        : VALIDATION_CODE_ERRORS['0'];
      // In such errorCode cases, application should redirect the user to the initial screen,
      // because cannot process the flow anyway
      if (errorCode === $loanConfirmationInterruptedCode) {
        const confirmTermsUrl = sessionStorage.getItem('confirmTermsUrl');
        if (confirmTermsUrl) {
          $verificationCodeModal.modal('hide');
          $errorModal.find('.error-message').html(message);
          $errorModal.modal('show');
        }
      }
    }

    if (message === 'hasError') {
      codeInputBlockClassList.add('invalid');
    } else {
      $errorBox.html(message);
    }
  }
};

$(document).on('input', '#codeInput input.lettering-input-text', function(e) {
  this.value = this.value.replace(/[^0-9]/g, '');

  if (this.value.length > 4) {
    this.value = this.value.slice(0, 4);
  }
});
