@import '../../telcellApp/src/styles/variables';

.contract-content {
  min-height: 100%;
  padding-top: 10px;

  .main-points {
    font-size: 12px;
    font-family: 'GHEA Grapalat';
  }

  .approval-box {
    text-align: center;
    padding: 20px;
    background-color: #eeeeee;
    margin-bottom: 20px;

    .checkbox {
      display: flex;
      justify-content: center;

      label {
        display: flex;
        align-items: center;
        font-weight: bold;
      }
    }

    .input-box {
      display: flex;
      justify-content: center;
      align-items: center;

      .input-group {
        display: flex;
        align-items: center;
        justify-content: center;
        max-width: 140px;

        input {
          outline: none;
          font-size: 16px;
          padding: 5px 30px 5px 10px;
          border-radius: 5px;
          border: 1px solid $gc-red-color;
          max-width: 110px;
        }

        span {
          position: relative;
          right: 30px;
          color: black;
          font-size: 16px;
        }
      }

      .info-text {
        font-size: 14px;
        margin: 10px 0;
        text-align: left;
        max-width: 400px;

        .apr-text {
          color: $gc-red-color;
        }
      }

      &.error {
        input {
          border: 1px solid red;
        }

        .info-text {
          color: red;

          .apr-text {
            color: red;
          }
        }
      }
    }
  }

  .modal {
    .modal-header {
      border-bottom: none;
      padding: 0 1rem;
    }

    .modal-content {
      border-radius: 1.3rem;

      .header-box {
        text-align: center;

        p {
          font-size: 14px;
          color: black;
          padding: 0 50px;
          margin-top: 5px;
        }

        .modal-title {
          color: $title-color;
          margin-top: 10px;
        }

        img {
          margin: 10px auto;
          height: 60px;
        }
      }

      .modal-body {
        background-color: $modal-bg-color;
        font-size: 16px;
        border-bottom-left-radius: 1.3rem;
        border-bottom-right-radius: 1.3rem;
        text-align: center;
        color: black;

        .btn.btn-primary {
          margin-top: 10px;
        }

        .body-box {
          .input-group {
            display: flex;
            justify-content: center;

            label {
              font-size: 16px;
              margin-bottom: 15px;
            }

            .error-box {
              margin-top: 10px;
            }

            button {
              margin-bottom: 0 !important;
            }

            &.code-box {
              #codeInput {
                input {
                  font-size: 1.9rem;
                }

                .lettering-input-container {
                  display: inline-flex;
                  width: 100%;
                  justify-content: center;

                  input[type='number']::-webkit-outer-spin-button,
                  input[type='number']::-webkit-inner-spin-button {
                    -webkit-appearance: none;
                    margin: 0;
                  }

                  input[type='number'] {
                    -moz-appearance: textfield;
                  }

                  input {
                    width: 120px;
                    height: 45px;
                    border: 1px solid $border-color;
                    border-radius: 0.5rem;
                    text-align: center;
                  }

                  &.invalid {
                    input {
                      border: 1px solid $pink !important;
                      background-color: $darker-skin;
                    }
                  }
                }
                .pincode-input-text,
                .form-control.pincode-input-text {
                  text-align: center;
                }

                .pincode-input-error {
                  clear: both;
                }
              }
            }
          }

          label {
            width: 100% !important;
          }
        }
      }
    }
  }
}

@media screen and (max-width: 770px) {
  .contract-content {
    .approval-box {
      .input-box {
        flex-direction: column;
      }
    }
  }
}

@media screen and (max-width: 425px) {
  .contract-content {
    .approval-box {
      .checkbox {
        label {
          &:before {
            left: 40px;
          }
          &:after {
            left: 50px;
          }
        }
      }
    }
  }
}

@media screen and (max-width: 380px) {
  .contract-content {
    .approval-box {
      .checkbox {
        label {
          &:before {
            left: 10px;
          }
          &:after {
            left: 20px;
          }
        }
      }
    }
  }
}
