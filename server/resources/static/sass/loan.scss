.loan-content {
  min-height: 100%;
  padding-top: 10px;

  h4 {
    font-size: 1.05rem;
  }

  .reject-section {
    margin-top: 20%;
    text-align: center;

    h4 {
      margin-top: 30px;
    }
  }

  .reject-section-bullets {
    text-align: center;
    max-width: 400px;
    margin: 0 auto;

    h4 {
      margin-top: 30px;
      color: #ff0000;
    }

    .error-description {
      text-align: left;
      padding: 0 10px;

      .reject-reason {
        font-size: 15px;
      }

      ul {
        font-size: 13px;
        list-style-type: none;
        padding-left: 10px;
        line-height: 20px;

        li::before {
          display: inline-block;
          width: 1em;
          margin-left: -1em;
          color: #934136;
          font-size: 15px;
          content: '•';
        }
      }
    }
  }

  .info-section {
    margin-top: 20px;
  }

  .button-section {
    margin-top: 30%;
  }

  .agreement-checkbox {
    width: 18px;
    height: 18px;
  }

  .agreement-checkbox-text {
    font-weight: 100;
  }

  .loan-block {
    margin: auto;
    width: 390px;
    padding: 10px;

    label {
      display: block;
    }

    select {
      padding: 8px;
      width: 350px;
      -webkit-appearance: none;
      background-color: $white;
      border: 1px solid gray;
      color: black;
    }

    select:focus {
      background-repeat: no-repeat;
      border-color: gray;
      outline: 0;
    }

    select.form-select {
      background-image: linear-gradient(45deg, transparent 51%, $main 50%),
        linear-gradient(135deg, $main 52%, transparent 50%);
      background-position: calc(100% - 20px) calc(1em + 2px),
        calc(100% - 15px) calc(1em + 2px), calc(100% - 2.5em) 0.5em;
      background-size: 5px 5px, 5px 5px, 1px 1.5em;
      background-repeat: no-repeat;
    }
  }

  hr {
    border: 1px solid $main;
  }
}
