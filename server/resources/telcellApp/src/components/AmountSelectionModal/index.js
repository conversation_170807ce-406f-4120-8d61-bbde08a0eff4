import React from 'react';
import { Input, Radio, Modal } from 'semantic-ui-react';
import { compose } from 'redux';
import { withRouter } from 'react-router-dom';
import { withNamespaces } from 'react-i18next';
import NumberFormat from 'react-number-format';
import { Form, Formik } from 'formik';
import classnames from 'classnames';

import Validation from '../Validation';
import { handlePreventEnterKey } from '../../helpers/input';
import { REQUESTED_LOAN_TYPE } from '../../constants';
import amountSelectionSchema from '../../validation/schemas/amountSelectionSchema';
import SvgComponent from '../SvgComponent';
import GCButton from '../GCButton';
import ErrorFocus from '../Validation/ErrorFocus';
import Loader from '../Loader';

import styles from './index.module.scss';

class AmountSelectionModal extends React.Component {
  handleSubmit = ({ amount, requestedType }) => {
    const { handlestoreRequestedDetails } = this.props;

    handlestoreRequestedDetails({
      amount: amount.toString().replace(/,/g, ''),
      requested_type: requestedType,
    });
  };

  handleRadioChange = (value, setFieldValue) => {
    setFieldValue('requestedType', value);
  };

  render() {
    const { t, loading, isOpen, handleCloseAmountSelectionModal } = this.props;

    return (
      <Modal
        id={styles.amount_selection_modal}
        dimmer="inverted"
        open={isOpen}
        centered={false}
      >
        <Formik
          initialValues={{
            amount: '',
            requestedType: null,
          }}
          onSubmit={this.handleSubmit}
          validationSchema={amountSelectionSchema}
          validateOnBlur={true}
          validateOnChange={false}
        >
          {props => {
            const { values, setFieldValue } = props;

            return (
              <Form onKeyDown={handlePreventEnterKey}>
                <div className={styles.modal_container}>
                  <SvgComponent
                    className={styles.currency_icon}
                    name="currency-icon"
                  />
                  <div className={styles.description}>
                    {t('amount_selection_modal.description')}
                  </div>
                  <div>
                    <Validation name="requestedType" showMessage={false}>
                      <div className={styles.radio_container}>
                        <p className={styles.radio_title}>
                          {t('amount_selection_modal.loan_type')}
                        </p>
                        <Radio
                          label={t('amount_selection_modal.pledge')}
                          name="requestedType"
                          value={values.requestedType}
                          className={styles.radio_section}
                          checked={
                            values.requestedType === REQUESTED_LOAN_TYPE.PLEDGE
                          }
                          onChange={() => {
                            this.handleRadioChange(
                              REQUESTED_LOAN_TYPE.PLEDGE,
                              setFieldValue
                            );
                          }}
                        />
                        <Radio
                          label={t('amount_selection_modal.trade')}
                          name="requestedType"
                          value={values.requestedType}
                          className={styles.radio_section}
                          checked={
                            values.requestedType === REQUESTED_LOAN_TYPE.TRADE
                          }
                          onChange={() => {
                            this.handleRadioChange(
                              REQUESTED_LOAN_TYPE.TRADE,
                              setFieldValue
                            );
                          }}
                        />
                      </div>
                    </Validation>
                  </div>
                  <div>
                    <p className={styles.input_title}>
                      {t('amount_selection_modal.amount_required')}
                    </p>
                    <Validation name="amount" showMessage={false}>
                      <div
                        className={classnames(
                          styles.amount_section,
                          'amount_selection_input_container'
                        )}
                      >
                        <NumberFormat
                          customInput={Input}
                          onValueChange={values => {
                            setFieldValue('amount', values.floatValue);
                          }}
                          value={values.amount}
                          autoComplete="off"
                          type="tel"
                          placeholder="֏ 0.00"
                          allowNegative={false}
                          thousandSeparator={true}
                          maxLength={9}
                          id="amount"
                          className={styles.amount_input}
                        />
                      </div>
                    </Validation>
                  </div>
                  <div>
                    <div className={styles.confirm_btn}>
                      <GCButton primary={true} type="submit">
                        {t('amount_selection_modal.confirm')}
                      </GCButton>
                    </div>
                    <div className={classnames(styles.cancel_btn)}>
                      <GCButton
                        primary={true}
                        onClick={handleCloseAmountSelectionModal}
                      >
                        {t('amount_selection_modal.cancel')}
                      </GCButton>
                    </div>
                  </div>
                  <Loader loading={loading} />

                  <ErrorFocus />
                </div>
              </Form>
            );
          }}
        </Formik>
      </Modal>
    );
  }
}

export default compose(
  withRouter,
  withNamespaces('translations')
)(AmountSelectionModal);
