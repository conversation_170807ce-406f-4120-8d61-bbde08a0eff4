import React, { Component } from 'react';
import { isEmpty } from 'lodash';
import { withNamespaces } from 'react-i18next';
import { Input } from 'semantic-ui-react';
import classnames from 'classnames';

import styles from './index.module.scss';

class AutoComplete extends Component {
  componentDidMount() {
    this.renderAutoComplete();
  }

  componentDidUpdate(prevProps) {
    const {
      carVerification,
      currentLocation,
      onLocationChange,
      onAddressChange,
    } = this.props;

    if (this.props !== prevProps.map) {
      this.renderAutoComplete();
    }

    if (
      isEmpty(currentLocation) &&
      carVerification &&
      carVerification.address
    ) {
      this.autocomplete.value = carVerification.address;

      onLocationChange({
        lat: carVerification.latitude,
        lng: carVerification.longitude,
      });

      onAddressChange(carVerification.address);
    }
  }

  renderAutoComplete = () => {
    const {
      google,
      map,
      onLocationChange,
      bounds,
      onAddress<PERSON>hange,
      setWrongAddress,
      isValid,
    } = this.props;

    if (!google || !map) {
      return;
    }

    const options = {
      bounds,
      componentRestrictions: { country: 'am' },
    };

    const autocomplete = new google.maps.places.Autocomplete(
      this.autocomplete,
      options
    );

    autocomplete.bindTo('bounds', map);

    autocomplete.addListener('place_changed', () => {
      const place = autocomplete.getPlace();

      if (!place.geometry) {
        return;
      }

      if (place.geometry.viewport) {
        map.fitBounds(place.geometry.viewport);
      } else {
        map.setCenter(place.geometry.location);
        map.setZoom(17);
      }

      const isWrongAddress = !bounds.contains(place.geometry.location);

      if (isWrongAddress) {
        this.autocomplete.value = '';
        onAddressChange('');
        setWrongAddress(true);
      } else {
        onAddressChange(place.formatted_address);
        onLocationChange(place.geometry.location);
        isValid(true);
        setWrongAddress(false);
      }
    });
  };

  handleEnterPress = e => {
    if (e.which === 13 || e.keyCode === 13) {
      e.preventDefault();
    }
  };

  render() {
    const {
      t,
      readonly = false,
      isWrongAddress,
      setErrors,
      onAddressChange,
      isValid,
    } = this.props;

    return (
      <>
        {isWrongAddress && (
          <div className={styles.error_message}>
            {t('loan.steps.car_verification.wrong_address')}
          </div>
        )}
        <Input
          fluid
          name="address"
          className={classnames(
            styles.autocomplete_input,
            isWrongAddress ? styles.error : ''
          )}
          placeholder={t('loan.steps.car_verification.address')}
          onBlur={() => {
            setErrors();
          }}
          onChange={() => {
            onAddressChange(this.autocomplete.value);
            isValid(false);
          }}
          onKeyPress={this.handleEnterPress}
          readOnly={readonly}
          error={isWrongAddress}
        >
          <input ref={ref => (this.autocomplete = ref)} />
        </Input>
      </>
    );
  }
}

export default withNamespaces('translations')(AutoComplete);
