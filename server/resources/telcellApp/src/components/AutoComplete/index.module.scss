@import '../../styles/variables';

.autocomplete_input {
  border: 1px $darky-gray;
  border-radius: 5px;
  position: absolute;
  width: 100%;
  color: $main-text-color;
  margin-bottom: 10px;

  input {
    font-size: 16px;
  }
}

.error {
  input {
    background-color: $darker-skin !important;
  }
}

.error_message {
  color: red;
  justify-content: left;
  font-size: 10px;
}

:global(.pac-container) {
  border: 1px solid gray;
  box-shadow: none;
}
