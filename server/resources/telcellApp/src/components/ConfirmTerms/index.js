import React, { Component } from 'react';
import { Checkbox } from 'semantic-ui-react';
import { compose } from 'redux';
import { Trans, withNamespaces } from 'react-i18next';
import { connect } from 'react-redux';

import SvgComponent from '../SvgComponent';
import GCButton from '../GCButton';
import { HOME_SCREEN_PDFS } from '../../constants';
import { API_URL_OTCL } from '../../config';
import { setSuuid } from '../../helpers/auth';
import { setLoanType } from '../../helpers';
import {
  composeCitizenInfo,
  storeRequestedDetails,
} from '../../store/ducks/citizen';
import Loader from '../Loader';
import AmountSelectionModal from '../AmountSelectionModal';

import ServerError from '../Validation/ServerError';

import styles from './index.module.scss';

class ConfirmTerms extends Component {
  constructor(props) {
    super(props);

    this.state = {
      agreementCheckboxOne: false,
      agreementCheckboxTwo: false,
      openAmountSelectionModal: false,
    };

    const { apiParams } = this.props;

    if (apiParams) {
      apiParams.suuid && setSuuid(apiParams.suuid);
      apiParams.loan_type_id && setLoanType(apiParams.loan_type_id);
    }
  }

  componentDidUpdate(prevProps) {
    const {
      citizen,
      composeCitizenInfo,
      storeAmountSuccess,
      error,
    } = this.props;
    // We need to hide modal in case of errors to show the message to the citizen
    if (prevProps.error !== error && error) {
      this.handleCloseAmountSelectionModal();
    }

    if (
      prevProps.submitDocumentSuccess !== storeAmountSuccess &&
      storeAmountSuccess
    ) {
      composeCitizenInfo();
    }

    if (prevProps.citizen !== citizen && citizen.success) {
      window.location.replace(`${API_URL_OTCL}/app/loan/loan-amount`);
    }
  }

  handleCheckboxChange(target) {
    const { name } = target;

    this.setState({ [name]: !this.state[name] });
  }

  handlestoreRequestedDetails = payload => {
    const { storeRequestedDetails } = this.props;

    storeRequestedDetails(payload);
  };

  handleConfirm = () => {
    this.setState({
      openAmountSelectionModal: true,
    });
  };

  handleCloseAmountSelectionModal = () => {
    this.setState({
      openAmountSelectionModal: false,
    });
  };

  render() {
    const { openAmountSelectionModal } = this.state;
    const { t, loading, error } = this.props;

    return (
      <>
        {loading && <Loader loading={true} />}
        <div className={styles.wrapper}>
          <div className={styles.container}>
            <div className={styles.header}>
              <SvgComponent name="cash_me" />
            </div>
            <div className={styles.warning_section}>
              <SvgComponent name="warning" className={styles.warn_icon} />
              <div>{t('terms_confirmation.warning_text')}</div>
            </div>
            <hr className={styles.line} />
            <div className={styles.checkboxes_section}>
              <Checkbox
                name="agreementCheckboxOne"
                id="agreementCheckboxOne"
                className={styles.checkbox}
                onChange={(event, target) => {
                  this.handleCheckboxChange(target);
                }}
                checked={this.state.agreementCheckboxOne}
                label={t('terms_confirmation.agreement_checkbox_one')}
              />
              <Checkbox
                name="agreementCheckboxTwo"
                id="agreementCheckboxTwo"
                className={styles.checkbox}
                onChange={(event, target) => {
                  this.handleCheckboxChange(target);
                }}
                checked={this.state.agreementCheckboxTwo}
                label={
                  <label>
                    <Trans
                      defaults={'terms_confirmation.agreement_checkbox_two'}
                      components={[
                        <a
                          target="_blank"
                          rel="noopener noreferrer"
                          href={HOME_SCREEN_PDFS.AGREEMENT}
                          className={styles.agreement_link}
                        >
                          text
                        </a>,
                      ]}
                    />
                  </label>
                }
              />
              <div className={styles.actions}>
                <GCButton
                  type="button"
                  className={styles.confirm_button}
                  primary={true}
                  disabled={
                    !(
                      this.state.agreementCheckboxOne &&
                      this.state.agreementCheckboxTwo
                    )
                  }
                  onClick={this.handleConfirm}
                >
                  {t('terms_confirmation.button')}
                </GCButton>
              </div>
            </div>
            {error && <ServerError error={error} />}
          </div>
        </div>
        <AmountSelectionModal
          handlestoreRequestedDetails={this.handlestoreRequestedDetails}
          handleCloseAmountSelectionModal={this.handleCloseAmountSelectionModal}
          isOpen={openAmountSelectionModal}
          loading={loading}
        />
      </>
    );
  }
}

const mapStateToProps = state => {
  const {
    citizen: { data: citizen = {}, loading, storeAmountSuccess, error },
  } = state;

  return { citizen, loading, storeAmountSuccess, error };
};

const mapDispatchToProps = dispatch => {
  return {
    composeCitizenInfo: () => dispatch(composeCitizenInfo()),
    storeRequestedDetails: payload => dispatch(storeRequestedDetails(payload)),
  };
};

export default compose(
  withNamespaces('translations'),
  connect(mapStateToProps, mapDispatchToProps)
)(ConfirmTerms);
