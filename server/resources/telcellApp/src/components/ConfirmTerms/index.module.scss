@import '../../styles/variables';

.wrapper {
  background-color: $white;
}

.container {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
  color: $dark-gray;
  font-size: 14px;
  max-width: 420px;
  font-family: 'DejaVu Sans Book Arm', sans-serif !important;

  :global(.server_error) {
    margin: 0;
    text-align: center;
    padding-bottom: 10px;
  }
}

.header {
  text-align: center;
  padding: 15px 0;

  svg {
    height: auto;
    width: 50%;
    max-width: 220px;
    min-width: 160px;
  }
}

.warning_section {
  display: flex;
  margin-top: 15px;

  .warn_icon {
    width: 40px;
    margin-right: 14px;
  }
}

.line {
  border: 0.5px solid $border-color;
  width: 100%;
  margin: 16px 0;
}

.checkboxes_section {
  input[type='checkbox'] ~ label::before,
  label::after {
    border-radius: 2px !important;
    border: 1px solid $gc-red-color !important;
    zoom: 1.4 !important;
    top: 3px !important;
    padding-top: 0 !important;
  }

  .checkbox {
    margin: 10px 0;

    label {
      padding-left: 10% !important;
      color: $dark-gray !important;
      font-size: 12px;
    }
  }

  .agreement_link {
    color: $darker-gray;
    text-decoration: underline;
  }
}

.actions {
  display: flex;
  justify-content: center;
  align-items: center;

  .confirm_button {
    background: $telcell-orange !important;
    max-width: 250px !important;
    margin: 15px 0 20px 0 !important;
  }
}
