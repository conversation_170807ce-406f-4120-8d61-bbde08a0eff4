import React from 'react';
import { withNamespaces } from 'react-i18next';
import GCButton from '../GCButton';

import styles from './index.module.scss';

const hasPrevious = (conf, step) => {
  const s = findStep(step, conf);
  return s && s.previous;
};

const hasNext = (conf, step) => {
  const s = findStep(step, conf);
  return s && s.next;
};

const findStep = (step, steps) => {
  return steps.find(s => s.id === step.id);
};

const Navigation = ({ t, wizardBag = {}, children }) => {
  const { goPrevious, step, config: steps } = wizardBag;

  if (children) {
    return <div id={styles.wizard_navigation}>{children}</div>;
  }

  return (
    <div id={styles.wizard_navigation}>
      {hasPrevious(steps, step) && (
        <GCButton
          type="button"
          onClick={() => goPrevious()}
          className={styles.previous_step_button}
        >
          {t(step.previous)}
        </GCButton>
      )}

      {hasNext(steps, step) && (
        <GCButton type="submit" primary={true}>
          {t(step.next)}
        </GCButton>
      )}
    </div>
  );
};

export default withNamespaces('translations')(Navigation);
