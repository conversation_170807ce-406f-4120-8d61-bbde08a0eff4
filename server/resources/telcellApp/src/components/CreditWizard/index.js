import React, { Component } from 'react';
import { connect } from 'react-redux';
import { with<PERSON>outer } from 'react-router-dom';
import { withNamespaces } from 'react-i18next';
import { Wizard, Steps, Step } from 'react-albus';
import { compose } from 'redux';
import querystring from 'query-string';
import classnames from 'classnames';

import WizardError from './WizardError';
import { EXPIRED_SUUID_ERROR_CODE, LOAN_TYPES } from '../../constants';
import {
  getLatestStep,
  getLoanType,
  setLatestStep,
  removeLatestStep,
} from '../../helpers';
import { wizardErrorClear } from '../../store/ducks/wizard';
import LoanAmountStep from '../LoanScreen/Components/LoanAmountStep';
import PersonalInfoStep from '../LoanScreen/Components/PersonalInfoStep';
import SmsValidationStep from '../LoanScreen/Components/SmsValidationStep';
import CarVerificationStep from '../LoanScreen/Components/CarVerificationStep';
import { API_URL_OTCL } from '../../config';

import styles from './index.module.scss';

const LOAN_AMOUNT_STEP = {
  id: 'loan-amount',
  description: 'loan.steps.loan_amount.description_ovl',
  previous: 'loan.steps.loan_amount.previous',
  content: props => {
    return <LoanAmountStep {...props} />;
  },
  next: 'loan.steps.loan_amount.next',
};
const PERSONAL_INFO_STEP = {
  id: 'personal-info',
  description: 'loan.steps.personal_info.description',
  content: props => {
    return <PersonalInfoStep {...props} />;
  },
  next: 'loan.steps.personal_info.next',
  previous: 'loan.steps.personal_info.previous',
};
const SMS_VALIDATION_STEP = {
  id: 'sms-validation',
  description: 'loan.steps.sms_validation.description',
  content: props => {
    return <SmsValidationStep {...props} />;
  },
  next: 'loan.steps.sms_validation.next',
  previous: 'loan.steps.sms_validation.previous',
};
const CAR_VERIFICATION_STEP = {
  id: 'car-verification',
  description: 'loan.steps.car_verification.description',
  content: props => {
    return <CarVerificationStep {...props} />;
  },
  next: 'loan.steps.car_verification.next',
  previous: 'loan.steps.car_verification.previous',
};

class CreditWizard extends Component {
  constructor(props) {
    super(props);

    this.stepContainers = this.composeSteps();
  }

  componentDidMount() {
    const {
      history,
      match: { url },
      wizardErrorClear,
    } = this.props;

    wizardErrorClear();

    const step = this.pathToStep(this.stepContainers, history, url);

    if (!step) {
      this.redirectHome();
    } else {
      setLatestStep(step.id);
    }
  }

  componentDidUpdate() {
    const {
      wizard: { error },
    } = this.props;

    if (error && error.code === EXPIRED_SUUID_ERROR_CODE) {
      this.redirectHome();
    }
  }

  composeSteps = () => {
    if (getLoanType() === LOAN_TYPES.OVL.id) {
      return [
        LOAN_AMOUNT_STEP,
        PERSONAL_INFO_STEP,
        CAR_VERIFICATION_STEP,
        SMS_VALIDATION_STEP,
      ];
    }

    return [];
  };

  redirectHome = () => {
    const { wizardErrorClear } = this.props;

    removeLatestStep();

    window.location.replace(`${API_URL_OTCL}/app/terms-and-conditions`);

    wizardErrorClear();
  };

  onClose = () => {
    this.props.wizardErrorClear();
  };

  pathToStep(stepContainers, history, url) {
    const pathname = history.location.pathname;
    let id = pathname.replace(url + '/', '');

    // because our loan-amount first request including token, our id is being e.g loan-amount/citizenToken
    // that's why we need to remove token for resolving correct step by path only for this case
    if (id.includes('loan-amount')) {
      id = 'loan-amount';
    }

    const [step] = stepContainers.filter(s => s.id === id);

    return step;
  }

  isActive(active, step, steps) {
    return (
      steps.findIndex(el => el.id === step.id) ===
      steps.findIndex(el => el.id === active.id)
    );
  }

  goTo = (history, step, url, params) => {
    setLatestStep(step.id);

    this.push(history, step, url, params);
  };

  push(history, step, url, params) {
    const query = params
      ? `?${querystring.stringify(params)}`
      : history.location.search;
    const path = `${url}/${step.id}${query}`;

    history.push(path);
  }

  _getLatestStep(step) {
    const stepId = getLatestStep() ? getLatestStep() : step.id;

    return this.stepContainers.find(s => s.id === stepId);
  }

  render() {
    const {
      t,
      history,
      match: { url },
      wizard: { error },
    } = this.props;

    const wizardStyles = (step, s, steps) =>
      classnames({
        [styles.counts]: true,
        [styles.active]: this.isActive(step, s, steps),
      });

    return (
      <Wizard
        history={history}
        basename={url}
        render={({ step, steps }) => {
          const latestStep = this._getLatestStep(step);
          const activeIndex =
            latestStep &&
            this.stepContainers.findIndex(el => el.id === latestStep.id);

          return (
            <>
              <div className={styles.wizard_container}>
                <div className={styles.wizard}>
                  {latestStep && (
                    <div className={styles.title_description}>
                      <span>{t(latestStep.description)}</span>
                      <span className={wizardStyles(step, latestStep, steps)}>
                        <span>{activeIndex + 1 + '/'}</span>
                        <span>{steps.length}</span>
                      </span>
                    </div>
                  )}
                </div>
                <WizardError error={error} onClose={this.onClose} />
              </div>
              <div className={styles.wizard_page}>
                <Steps step={latestStep || (step.id && step)}>
                  {this.stepContainers.map(s => {
                    const { id } = s;
                    const StepContent = s.content;

                    const wizardBag = {
                      goNext: params => {
                        const index = this.stepContainers.indexOf(s);
                        const nextStep = this.stepContainers[index + 1];

                        this.goTo(history, nextStep, url, params);
                      },
                      goPrevious: params => {
                        const index = this.stepContainers.indexOf(s);
                        const previousStep = this.stepContainers[index - 1];

                        this.goTo(history, previousStep, url, params);
                      },
                      config: this.stepContainers,
                      step: s,
                    };

                    return (
                      <Step id={id} key={id}>
                        <StepContent wizardBag={wizardBag} />
                      </Step>
                    );
                  })}
                </Steps>
              </div>
            </>
          );
        }}
      />
    );
  }
}

function mapStateToProps(state) {
  const { wizard } = state;

  return { wizard };
}

const mapDispatchToProps = dispatch => {
  return {
    wizardErrorClear: () => dispatch(wizardErrorClear()),
  };
};

export default compose(
  withRouter,
  connect(mapStateToProps, mapDispatchToProps),
  withNamespaces('translations')
)(CreditWizard);
