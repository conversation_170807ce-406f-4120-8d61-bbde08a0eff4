import React from 'react';
import { Button } from 'semantic-ui-react';
import classnames from 'classnames';
import './index.module.scss';
import SvgComponent from '../SvgComponent';

const GCButton = ({
  children,
  className,
  primary = false,
  type = 'submit',
  disabled,
  loading,
  onClick,
  arrowIcon,
}) => {
  const clsName = primary ? 'gc_primary' : 'gc_secondary';

  return (
    <Button
      type={type}
      className={classnames(clsName, className)}
      disabled={disabled}
      loading={loading}
      onClick={onClick}
    >
      {children}
      {arrowIcon && <SvgComponent name="arrow" />}
    </Button>
  );
};

export default GCButton;
