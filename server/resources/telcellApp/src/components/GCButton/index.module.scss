@import '../../styles/variables';

:global .ui.button {
  &.gc_primary,
  &.gc_primary:hover,
  &.gc_primary:focus {
    width: 100%;
    height: 46px;
    color: $white;
    font-size: 18px;
    text-transform: uppercase;
    border-radius: 3px;
    background: $orange;
    cursor: pointer;
    font-weight: normal;

    svg {
      vertical-align: middle;
      float: right;
      height: 21px;

      path {
        fill: $white;
      }
    }
  }

  &.gc_secondary,
  &.gc_secondary:hover,
  &.gc_secondary:focus {
    color: $secondary;
    font-size: 18px;
    font-weight: bold;
    background: none;
    cursor: pointer;
  }
}
