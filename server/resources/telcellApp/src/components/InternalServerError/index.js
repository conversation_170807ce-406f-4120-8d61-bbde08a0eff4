import React from 'react';
import { withNamespaces } from 'react-i18next';

import SvgComponent from '../SvgComponent';

import styles from './index.module.scss';

export const InternalServerError = props => {
  const { t } = props;

  return (
    <div className={styles.internal_server_error_container}>
      <SvgComponent name="internal-server-error" className={styles.image} />
      <div className={styles.error_msg}>
        {t('loan.internal_server_error_text')}
      </div>
    </div>
  );
};

export default withNamespaces('translations')(InternalServerError);
