import React from 'react';
import { Dimmer, Loader } from 'semantic-ui-react';
import PropTypes from 'prop-types';

const LoaderComponent = ({ loading }) => {
  return (
    <>
      {loading && (
        <div>
          <Dimmer inverted active>
            <Loader />
          </Dimmer>
        </div>
      )}
    </>
  );
};

LoaderComponent.propTypes = {
  loading: PropTypes.bool,
};

LoaderComponent.defaultProps = {
  loading: false,
};

export default LoaderComponent;
