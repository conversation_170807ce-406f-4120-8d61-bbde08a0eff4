import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Dropdown, Icon } from 'semantic-ui-react';
import { withNamespaces } from 'react-i18next';

import Currency from '../Currency';

import styles from './index.module.scss';

class LoanAmount extends Component {
  constructor(props) {
    super(props);

    this.state = {
      amount: 0,
      value: this.props.selectedAmount || this.props.max,
    };
  }

  componentDidUpdate(prevProps) {
    const { max, onUpdateAmount, currentOvl } = this.props;

    if (prevProps.currentOvl !== currentOvl) {
      this.setState({ value: max });

      onUpdateAmount(max);
    }
  }

  onChangeAmount = value => {
    const { onUpdateAmount } = this.props;
    this.setState({ value });
    onUpdateAmount(value);
  };

  render() {
    const { t, min, max, step, disabled } = this.props;
    let { value } = this.state;
    let stateOptions = [];
    let i = 0;

    for (let amount = min; amount <= max; amount += step) {
      stateOptions.push({
        key: i++,
        text: (
          <span className={styles.loan_data}>
            <Currency hasSign={true} value={amount} precision={false} />
          </span>
        ),
        value: amount,
      });
    }

    if (
      stateOptions.length &&
      stateOptions[stateOptions.length - 1].value < max
    ) {
      stateOptions.push({
        key: i++,
        text: (
          <span className={styles.loan_data}>
            <Currency hasSign={true} value={max} precision={false} />
          </span>
        ),
        value: max,
      });
    }

    return (
      <div className={styles.loan_amount_container}>
        <div className={styles.loan_amount_info_section}>
          <div className={styles.label}>
            {t('loan.steps.loan_amount.available_max_amount')}
          </div>
          <div className={styles.value}>
            <Currency hasSign={true} value={max} precision={false} />
          </div>
        </div>
        <div className={styles.loan_amount_dropdown}>
          <div className={styles.loan_amount_info_section}>
            <div className={styles.label}>
              {t('loan.steps.loan_amount.select_loan_amount')}
            </div>
            <div className={styles.value}>
              <Dropdown
                placeholder={t('loan.steps.loan_amount.select_loan_amount')}
                fluid
                selection
                value={value}
                icon={<Icon name="chevron down" className={styles.icon} />}
                options={stateOptions}
                onChange={(event, data) => {
                  this.onChangeAmount(data.value);
                }}
                selectOnBlur={false}
                disabled={disabled}
              />
            </div>
          </div>
        </div>
      </div>
    );
  }
}

LoanAmount.propTypes = {
  onChange: PropTypes.func,
};

LoanAmount.defaultProps = {
  onChange: () => {},
};

export default withNamespaces('translations')(LoanAmount);
