@import '../../styles/variables';
@import '../../styles/sizes';

.loan_amount_container {
  .loan_amount_info_section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-bottom: 1px solid $gray;

    .label {
      font-size: 14px;
    }

    .value {
      font-size: 18px;
      font-weight: bold;

      svg {
        height: 13px !important;
        width: 20px;
      }
    }
  }

  .loan_amount_dropdown {
    .loan_amount_info_section {
      padding: 11px 0;
      flex-direction: column;
      align-items: flex-start;

      .value {
        margin-top: 5px;
        width: 100%;
      }
    }

    .icon {
      color: $main !important;
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
      transition-duration: 0.3s;
      font-size: 20px !important;
    }

    .loan_data {
      display: flex;
      font-size: 18px;
      line-height: 18px;

      svg {
        fill: $black;
        height: 14px;
      }
    }

    :global .menu {
      max-height: 200px;
    }

    :global .dropdown {
      min-width: 160px;
      color: $main;

      svg {
        fill: $main;
        stroke: $main;
      }

      &.active {
        svg {
          fill: $black;
          stroke: $black;
        }

        .icon {
          transform: translateY(-50%) rotate(180deg);
        }
      }
    }
  }
}
