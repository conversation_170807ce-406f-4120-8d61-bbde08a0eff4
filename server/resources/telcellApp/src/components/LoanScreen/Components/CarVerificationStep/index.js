import React, { Component } from 'react';
import { withNamespaces } from 'react-i18next';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { Text<PERSON><PERSON>, Icon, Menu, Dropdown } from 'semantic-ui-react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { Formik, Form } from 'formik';
import { Map, GoogleApiWrapper, Polygon } from 'google-maps-react';

import wizardCarVerificationDataSchema from '../../../../validation/schemas/wizardCarVerificationDataSchema';
import Navigation from '../../../CreditWizard/Navigation';
import ErrorFocus from '../../../Validation/ErrorFocus';
import {
  CHECKUP_DATE_FORMAT,
  TIME_FORMAT,
  BIRTH_DATE_FORMAT,
  GLOBALCREDIT_LAT,
  GLOBALCREDIT_LNG,
  YEREVAN_COORDINATES,
  TRANSFER_METHODS,
  ARM_TIMEZONE,
  HOUR,
} from '../../../../constants';
import AutoComplete from '../../../AutoComplete';
import Loader from '../../../Loader';
import Validation from '../../../Validation';
import { handlePreventEnterKey } from '../../../../helpers/input';

import { fetchAgentSchedules } from '../../../../store/ducks/agentSchedules';
import {
  storeCarVerificationData,
  fetchCarVerificationData,
  resetCarVerificationData,
  carVerificationErrorClear,
} from '../../../../store/ducks/carVerification';
import { storeTransferInfo } from '../../../../store/ducks/loan';
import { fetchServerTime } from '../../../../store/ducks/appConfigs';

import styles from './index.module.scss';

class CarVerificationStep extends Component {
  constructor(props) {
    super(props);

    this.menuItems = [];

    this.state = {
      activeItem: {},
      currentLocation: {},
      checkupDate: '',
      notes: '',
      isWrongAddress: false,
      isValidAddress: true,
    };
  }

  componentDidMount() {
    window.scroll(0, 0);

    this.props.serverTime();
    this.props.fetchAgentSchedules();
  }

  componentDidUpdate(prevProps) {
    const {
      carVerification,
      agentSchedules,
      fetchCarVerificationData,
    } = this.props;

    if (
      carVerification?.error?.errors?.checkupDate &&
      this.state.checkupDate !== ''
    ) {
      this.props.serverTime();

      this.setState(
        {
          checkupDate: '',
        },
        () => this.props.carVerificationErrorClear()
      );
    }

    if (
      agentSchedules !== prevProps.agentSchedules &&
      agentSchedules.data &&
      !agentSchedules.loading
    ) {
      this.menuItems = agentSchedules.data.map(item => ({
        name: this.composeWeekdayNames(item.weekday.date),
        value: this.composeDays(item.weekday.date),
        text: this.composeWeekdayNames(item.weekday.date),
        availableHours: this.composeHours(item.hours),
      }));

      fetchCarVerificationData();
    }

    if (
      carVerification.data &&
      prevProps.carVerification.data !== carVerification.data &&
      !carVerification.loading
    ) {
      const activeItem = this.getActiveItem(carVerification.data);
      const _carVerification =
        carVerification.data.mortgage || carVerification.data;

      this.setState({
        activeItem,
        checkupDate:
          _carVerification.checkupDate &&
          moment(_carVerification.checkupDate).format(CHECKUP_DATE_FORMAT),
        address: _carVerification.address,
        notes: _carVerification.notes || '',
        currentLocation: this.getCoordinates(
          +_carVerification.latitude,
          +_carVerification.longitude
        ),
      });
    }
  }

  componentWillUnmount() {
    this.props.resetCarVerificationData();
  }

  composeHours = hours => {
    return hours.map(h => {
      return moment(h).format(CHECKUP_DATE_FORMAT);
    });
  };

  getActiveItem = data => {
    if (data.checkupDate) {
      const activeItem = this.menuItems.find(item => {
        return (
          item.value === moment(data.checkupDate).format(BIRTH_DATE_FORMAT)
        );
      });

      if (activeItem) {
        return activeItem;
      }
    }

    return this.state.activeItem;
  };

  getCoordinates = (lat, lng) => {
    if (lat && lng) {
      return {
        lat,
        lng,
      };
    }

    return this.state.currentLocation;
  };

  handleWeekdayClick = (item, values, setFieldValue) => {
    this.setCheckupDate(setFieldValue, '');
    this.onChangeCheckupDate('');

    this.setState({
      activeItem: item,
    });
  };

  composeWeekdayNames = date => {
    const { t } = this.props;

    return t(`weekdays.${moment(date).day()}`);
  };

  setCheckupDate = (setFieldValue, date) => {
    setFieldValue('checkupDate', date);
  };

  composeDays = date => {
    return moment(date).format(BIRTH_DATE_FORMAT);
  };

  hoursForWeekday = item => {
    const { appConfigs } = this.props;
    const { availableHours = [] } = item;

    return availableHours.map((h, i) => {
      const time = moment(h)
        .utc(h)
        .utcOffset(ARM_TIMEZONE)
        .format(TIME_FORMAT);
      const disabled = moment(h).isBefore(
        moment(appConfigs.data ? appConfigs.data.now : '')
          .add(1, HOUR)
          .format(CHECKUP_DATE_FORMAT)
      );

      return {
        id: i,
        text: time,
        value: h,
        disabled,
      };
    });
  };

  onMapClick = (mapProps, map, clickEvent) => {
    this.setState({
      currentLocation: {
        lat: clickEvent.latLng.lat(),
        lng: clickEvent.latLng.lng(),
      },
    });
  };

  onLocationChange = location => {
    const isFunction = typeof location.lat === 'function';

    this.setState({
      currentLocation: {
        lat: isFunction ? location.lat() : location.lat,
        lng: isFunction ? location.lng() : location.lng,
      },
    });
  };

  setWrongAddress = isWrongAddress => {
    this.setState({
      isWrongAddress,
    });
  };

  onChangeCheckupDate = checkupDate => {
    this.setState({
      checkupDate,
    });
  };

  getCarVerificationData = carVerification => {
    if (carVerification.data && carVerification.data.mortgage) {
      return carVerification.data.mortgage;
    }
    return carVerification.data;
  };

  onChangeAddress = address => {
    this.setState({
      address,
    });
  };

  scrollToError = () => {
    const element = document.getElementById('address');

    this.setWrongAddress(true);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  isLoading() {
    const { agentSchedules, carVerification, loan } = this.props;

    return (
      carVerification.loading ||
      agentSchedules.loading ||
      loan.transferInfoLoading
    );
  }

  centerMoved = (mapProps, map) => {
    this.setState({
      currentLocation: {
        lat: map.center.lat(),
        lng: map.center.lng(),
      },
    });
  };

  setBounds() {
    const bounds = new this.props.google.maps.LatLngBounds();
    const points = YEREVAN_COORDINATES;

    for (var i = 0; i < points.length; i++) {
      bounds.extend(points[i]);
    }

    return bounds;
  }

  handleSubmit = (payload, { setErrors }) => {
    const {
      wizardBag: { goNext },
      storeCarVerificationData,
      storeTransferInfo,
    } = this.props;

    const { isWrongAddress, isValidAddress } = this.state;

    if (isWrongAddress || !isValidAddress) {
      setErrors({ address: 'address' });
      this.scrollToError();

      return;
    }

    storeCarVerificationData(payload, () =>
      // Because we do not have transfer method selection step, we should store it manually
      storeTransferInfo(
        {
          route: TRANSFER_METHODS[0].route,
        },
        goNext
      )
    );
  };

  isValidAddress = isValidAddress => {
    this.setState({
      isValidAddress,
    });
  };

  render() {
    const { t, wizardBag, carVerification } = this.props;
    const {
      activeItem,
      currentLocation,
      checkupDate,
      address,
      notes,
      isWrongAddress,
    } = this.state;

    return (
      <Formik
        initialValues={{
          checkupDate,
          address,
          notes,
          latitude: currentLocation.lat,
          longitude: currentLocation.lng,
        }}
        enableReinitialize={true}
        onSubmit={this.handleSubmit}
        validationSchema={wizardCarVerificationDataSchema}
        validateOnBlur={true}
        validateOnChange={false}
      >
        {props => {
          const { values, setFieldValue, setErrors } = props;

          return (
            <Form
              id={styles.car_verification}
              onKeyDown={handlePreventEnterKey}
            >
              <Loader loading={this.isLoading()} />

              <div className={styles.car_verification_info}>
                {t('loan.steps.car_verification.info')}
              </div>
              <div className={styles.car_verification_container}>
                <div className={styles.car_verification_item}>
                  <div className={styles.car_verification_section}>
                    <div className={styles.car_verification_section_title}>
                      {t('loan.steps.car_verification.first_section_title')}
                    </div>
                    <div className={styles.car_verification_section_content}>
                      <Menu secondary className={styles.car_verification_menu}>
                        {this.menuItems.map(item => (
                          <Menu.Item
                            key={item.name}
                            name={item.name}
                            active={activeItem === item}
                            onClick={() => {
                              this.handleWeekdayClick(
                                item,
                                values,
                                setFieldValue
                              );
                            }}
                            className={styles.car_verification_section_dates}
                          >
                            <Icon name="check" />
                            <div
                              className={
                                styles.car_verification_section_dates_text
                              }
                            >
                              <div
                                className={
                                  styles.car_verification_section_dates_name
                                }
                              >
                                {item.text}
                              </div>
                              <div
                                className={
                                  styles.car_verification_section_dates_value
                                }
                              >
                                {item.value}
                              </div>
                            </div>
                          </Menu.Item>
                        ))}
                      </Menu>
                      <div className={styles.time_dropdowns_container}>
                        <label htmlFor="checkupDate">
                          <span>
                            {t(
                              'loan.steps.car_verification.first_section_sub_title'
                            )}
                          </span>
                        </label>
                        <Validation name="checkupDate" showMessage={false}>
                          <div className={styles.time_dropdown}>
                            <Dropdown
                              fluid
                              selection
                              id="checkupDate"
                              icon={
                                <Icon
                                  name="chevron down"
                                  className={styles.time_dropdown_icon}
                                />
                              }
                              className={styles.dropdown}
                              placeholder={t(
                                'loan.steps.car_verification.time'
                              )}
                              {...(!values.checkupDate &&
                                !checkupDate && {
                                  text: t('loan.steps.car_verification.time'),
                                })}
                              selectOnNavigation={false}
                              selectOnBlur={false}
                              value={values.checkupDate || checkupDate}
                              onChange={(event, data) => {
                                setFieldValue('checkupDate', data.value);
                                this.onChangeCheckupDate(data.value);
                              }}
                              options={this.hoursForWeekday(activeItem)}
                            />
                          </div>
                        </Validation>
                      </div>
                    </div>
                  </div>
                </div>
                <div className={styles.car_verification_item} id="address">
                  <div className={styles.car_verification_section}>
                    <div className={styles.car_verification_section_title}>
                      {t('loan.steps.car_verification.second_section_title')}
                    </div>
                    <div className={styles.car_verification_section_content}>
                      <div
                        className={styles.car_verification_section_content_map}
                      >
                        <Validation name="address" showMessage={false}>
                          <div name="address" className={styles.map_container}>
                            <Map
                              google={this.props.google}
                              zoom={10}
                              initialCenter={{
                                lat: GLOBALCREDIT_LAT,
                                lng: GLOBALCREDIT_LNG,
                              }}
                              center={currentLocation}
                              onDragend={this.centerMoved}
                              mapTypeControl={false}
                              streetViewControl={false}
                              gestureHandling="auto"
                              zoomControl={true}
                            >
                              <Polygon
                                paths={YEREVAN_COORDINATES}
                                strokeColor="#0000FF"
                                strokeOpacity={0.8}
                                strokeWeight={2}
                                onClick={this.onMapClick}
                                fillOpacity={0.1}
                              />
                              <AutoComplete
                                value={values.address}
                                {...props}
                                currentLocation={currentLocation}
                                carVerification={this.getCarVerificationData(
                                  carVerification
                                )}
                                isWrongAddress={isWrongAddress}
                                bounds={this.setBounds()}
                                onLocationChange={this.onLocationChange}
                                setWrongAddress={this.setWrongAddress}
                                isValid={this.isValidAddress}
                                onAddressChange={address => {
                                  setFieldValue('address', address);
                                  this.onChangeAddress(address);
                                }}
                                setErrors={() => {
                                  setErrors({ address: 'address' });
                                }}
                                readonly={false}
                              />
                            </Map>
                            <div className={styles.center_marker} />
                          </div>
                        </Validation>
                      </div>
                    </div>
                  </div>
                </div>
                <div className={styles.car_verification_item}>
                  <div className={styles.car_verification_section}>
                    <Validation name="notes" showMessage={false}>
                      <TextArea
                        name="notes"
                        className={
                          styles.car_verification_section_content_textarea
                        }
                        value={values.notes}
                        placeholder={t(
                          'loan.steps.car_verification.third_section_title'
                        )}
                      />
                    </Validation>
                  </div>
                </div>
              </div>
              <Navigation wizardBag={wizardBag} />

              <ErrorFocus serverError={carVerification.error} />
            </Form>
          );
        }}
      </Formik>
    );
  }
}

CarVerificationStep.propTypes = {
  wizardBag: PropTypes.object,
};

const mapStateToProps = state => {
  const { agentSchedules, carVerification, loan, appConfigs } = state;

  return { agentSchedules, carVerification, loan, appConfigs };
};

const mapDispatchToProps = dispatch => {
  return {
    fetchAgentSchedules: () => dispatch(fetchAgentSchedules()),
    storeCarVerificationData: (data, cb) =>
      dispatch(storeCarVerificationData(data, cb)),
    fetchCarVerificationData: () => dispatch(fetchCarVerificationData()),
    storeTransferInfo: (data, cb) => dispatch(storeTransferInfo(data, cb)),
    serverTime: () => dispatch(fetchServerTime()),
    resetCarVerificationData: () => dispatch(resetCarVerificationData()),
    carVerificationErrorClear: () => dispatch(carVerificationErrorClear()),
  };
};

export default compose(
  withNamespaces('translations'),
  connect(mapStateToProps, mapDispatchToProps),
  GoogleApiWrapper(props => ({
    apiKey: process.env.MIX_REACT_APP_GOOGLE_MAP_KEY,
    language: props.lng,
  }))
)(CarVerificationStep);
