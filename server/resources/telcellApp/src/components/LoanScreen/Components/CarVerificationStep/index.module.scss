@import '../../../../styles/variables';
@import '../../../../styles/sizes';

#car_verification {
  width: 100%;
  margin: auto;
  text-align: center;
  background: $white;
  padding: 0 15px;

  .car_verification_info {
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
    font-size: 14px;
    color: $black;
    padding-top: 20px;
  }

  .car_verification_container {
    width: 600px;
    margin: 30px auto 0 auto;
    text-align: left;
    position: relative;

    .car_verification_item {
      .car_verification_section {
        .car_verification_section_title {
          font-size: 20px;
          color: $black;
          text-align: left;
        }

        .car_verification_section_content {
          position: relative;
          padding: 0;
          height: fit-content;
          margin-top: 15px;

          .car_verification_menu {
            text-align: center;
            flex-direction: column;

            .car_verification_section_dates {
              width: 100%;
              height: 52px;
              margin-top: 10px;
              border-radius: 25px;
              background-color: $white;
              color: $darky-gray;
              display: flex;
              padding: 0 25px;
              border: none;
              box-shadow: 0 2px 20px $box-shadow-gray;

              &:first-child {
                margin-top: 0;
              }

              i {
                font-size: 25px;
                margin-right: 25px;
                font-weight: 100;
              }

              .car_verification_section_dates_text {
                font-size: 14px;
                width: 100%;
                display: flex;
                justify-content: space-between;
              }
            }

            .car_verification_section_dates:global(.active) {
              background-color: $white;
              border: 1px solid $main;
              color: $main;
            }
          }

          .car_verification_section_content_map {
            height: 300px;
            position: relative;

            input {
              border: none;
              border-bottom: 1px solid $gray;
              border-radius: 0;
              font-size: 14px;
            }
          }

          .car_verification_section_content_address {
            color: $purple-blue;
            margin-top: 10px;
            margin-left: 10px;
            text-align: left;
          }

          .car_verification_section_content_map {
            .map_container > div > div:first-child {
              margin-top: 60px;
            }

            .map_container {
              .center_marker {
                position: absolute;
                background: url(http://maps.gstatic.com/mapfiles/markers/marker.png)
                  no-repeat;
                top: 50%;
                left: 50%;
                z-index: 1;
                margin-left: -10px;
                height: 34px;
                width: 20px;
                cursor: pointer;
              }
            }
          }

          .time_dropdowns_container {
            margin: 20px auto;
            display: flex;
            flex-direction: column;

            .time_dropdown {
              width: 100%;
              display: inline-block;
              margin-top: 5px;

              .dropdown {
                padding: 12px 16px;
                height: 41px;
                line-height: 17px;
                border-radius: 25px;
                border: none;
                box-shadow: 0 2px 20px $box-shadow-gray;
                font-size: 14px;
              }

              .time_dropdown_icon {
                float: right;
              }

              :global(.ui.selection.active.dropdown .menu) {
                border: none;
              }
            }

            label {
              color: $dark-gray;
              font-size: 12px;
            }
          }
        }

        .car_verification_section_content_textarea {
          width: 100%;
          height: 90px;
          resize: none;
          margin-top: 20px;
          font-size: 14px;
          margin-bottom: 8px;
          padding: 10px;
          border: 1px solid $gray;

          &::placeholder {
            color: $gray;
          }

          &:focus {
            outline: none;
          }
        }
      }
    }
  }
}

@media screen and (max-width: $lowers-tablet-width) {
  #car_verification {
    width: 100%;

    .car_verification_info {
      margin: 0 auto 30px auto;
    }

    .car_verification_container {
      width: 100%;
      margin: 0 auto;

      .car_verification_item {
        .car_verification_section {
          display: block;

          .car_verification_section_content {
            .car_verification_menu {
              width: 100%;
              justify-content: center;
              margin: 0;
            }

            .time_dropdowns_container {
              label {
                font-size: 10px;
              }
            }
          }
        }
      }
    }
  }
}

@media screen and (max-width: $mobile-width) {
  #car_verification {
    .car_verification_info {
      max-width: 300px;
      font-size: 12px;
    }

    .car_verification_container {
      .car_verification_item {
        .car_verification_section {
          .car_verification_section_title {
            font-size: 16px;
          }

          .car_verification_section_content {
            .car_verification_menu {
              width: 100%;
              justify-content: center;
              margin: 0;
            }

            .car_verification_section_content_map {
              height: 200px;
            }

            .car_verification_section_content_address {
              margin-top: 3px;
              font-size: 11px;
              line-height: 14px;
            }
          }
        }
      }
    }
  }
}
