import React, { Component } from 'react';
import { Segment } from 'semantic-ui-react';
import { withNamespaces, Trans } from 'react-i18next';
import Currency from '../../../Currency';
import { compose } from 'redux';
import { connect } from 'react-redux';
import moment from 'moment';
import classnames from 'classnames';
import querystring from 'query-string';
import { Map, InfoWindow, <PERSON>er, GoogleApiWrapper } from 'google-maps-react';
import _ from 'lodash';
import { withRouter } from 'react-router-dom';
import ShowMoreText from 'react-show-more-text';

import { PHONE_NUMBER, PHONE_NUMBER_2 } from '../../../../constants';
import {
  TRANSFER_METHODS,
  CONFIRMED_NO_PAY,
  FINISHED,
  REJECTED,
  FINISHED_WITHOUT_MODERATION,
  FAILED_PAYMENT,
  LOAN_TYPES,
} from '../../../../constants';
import Loader from '../../../Loader';

import { fetchLoanByPublicId } from '../../../../store/ducks/loan';
import { fetchCashOfficesByPublicId } from '../../../../store/ducks/cashOffices';
import { openDocument } from '../../../../helpers';
import { fetchServerTime } from '../../../../store/ducks/appConfigs';
import { composeOffices } from '../../../../helpers/office';

import confirmation_icon from '../../../../images/confirmation_icon.svg';
import contract_icon from '../../../../images/contract.svg';
import download_icon from '../../../../images/download-icon.svg';
import info_icon from '../../../../images/info-icon.svg';
import warn_icon from '../../../../images/warn-icon.svg';
import fail_icon from '../../../../images/fail-icon.svg';
import SvgComponent from '../../../SvgComponent';

import styles from './index.module.scss';

const CASH_OFFICES = 'CASH_OFFICES';
const MAP = 'MAP';

class Confirmation extends Component {
  constructor(props) {
    super(props);

    this.state = {
      isShowMoreTextToggled: false,
    };
  }

  componentDidMount() {
    window.scroll(0, 0);

    const {
      location: { search },
      history,
    } = this.props;

    const { public_id } = querystring.parse(search);

    if (!public_id) {
      history.push({
        pathname: '/terms-and-conditions',
      });
    }

    this.props.fetchServerTime();
    this.props.fetchLoanByPublicId(public_id);
    this.props.fetchCashOfficesByPublicId(public_id);
  }

  isConfirmedNoPay = () => {
    const {
      loan: { data },
    } = this.props;

    return data.status === CONFIRMED_NO_PAY;
  };

  isPaymentFailed = () => {
    const {
      loan: { data },
    } = this.props;

    return data.status === FAILED_PAYMENT;
  };

  isPaymentFallenBack = () => {
    const {
      loan: { data },
    } = this.props;

    return data.failedPayment;
  };

  handleToggleShowMoreText = () => {
    this.setState({
      isShowMoreTextToggled: !this.state.isShowMoreTextToggled,
    });
  };

  getOfficeIconClass = office => {
    const { name } = office.item.cashOfficeType;

    return classnames({
      [styles.icon]: true,
      [styles.telcell]: name === 'Telcell',
      [styles.global_credit]: name === 'GlobalCredit',
    });
  };

  infoText = (loanTransfer, officesData) => {
    const finished = [FINISHED, FINISHED_WITHOUT_MODERATION];

    const {
      loan: { data },
      t,
    } = this.props;

    if (finished.includes(data.status)) {
      return t(`loan.steps.confirmation.moderation`);
    }

    if (data.status === REJECTED) {
      return t(`loan.steps.confirmation.rejection`);
    }

    // When loan type is OVL we should check if citizen is married and show proper information text
    if (data && data.loanTypeId === LOAN_TYPES.OVL.id) {
      let text;
      if (data.seller) {
        text = t(`loan.steps.confirmation.ovl_trade_confirmed`);
      } else {
        text = t(`loan.steps.confirmation.ovl_confirmed`);
      }

      return (
        <ShowMoreText
          lines={3}
          more={t('loan.steps.confirmation.show_more')}
          less={t('loan.steps.confirmation.show_less')}
          anchorClass="toggle-description-btn"
          expanded={false}
          className={classnames({
            [styles.ovl_confirmation_text]: true,
            [styles.show_more_text_toggled]: this.state.isShowMoreTextToggled,
          })}
          onClick={this.handleToggleShowMoreText}
        >
          <Trans defaults={text} />
        </ShowMoreText>
      );
    }

    if (this.isConfirmedNoPay()) {
      return t(`loan.steps.confirmation.confirmed_no_pay`, [
        PHONE_NUMBER,
        PHONE_NUMBER_2,
      ]);
    } else if (this.isPaymentFailed()) {
      return (
        <Trans
          defaults="loan.steps.confirmation.failed_payment"
          components={[<a href={`tel:${PHONE_NUMBER}`}>text</a>]}
        />
      );
    } else if (this.isPaymentFallenBack()) {
      const cashOfficesDetails = this.getCashOfficesDetails(officesData);

      return t(`loan.steps.confirmation.confirmed_failed_payment`, {
        cashOfficesDetails,
        withdrawExpDays: data.loanExpirationDays,
      });
    }

    if (loanTransfer.type === 'cash') {
      const cashOfficesDetails = this.getCashOfficesDetails(officesData);

      return t(`loan.steps.confirmation.${loanTransfer.method}`, {
        cashOfficesDetails,
        withdrawExpDays: data.loanExpirationDays,
      });
    }

    return t(`loan.steps.confirmation.${loanTransfer.method}`);
  };

  getCashOfficesDetails = officesData => {
    const { t } = this.props;

    const cashOfficeNames = _.keys(
      _.keyBy(officesData, function(el) {
        const officeName = el.cashOfficeType.nameHy;
        const phoneNumber = el.cashOfficeType.phoneNumber;
        // Output eg. ԹԵԼ-ՍԵԼ (հեռ․ 060 272222)
        return `${officeName} (${t(`loan.steps.confirmation.phone_number_abbreviation`)} ${phoneNumber})`;
      })
    );

    return cashOfficeNames.join(', ');
  };

  shouldDisplay(type) {
    if (type === CASH_OFFICES) {
      return true;
    }

    if (type === MAP) {
      return true;
    }
  }

  render() {
    const {
      t,
      loan: { data, loading },
      cashOffices,
      appConfigs,
    } = this.props;

    const loanTransfer = TRANSFER_METHODS.find(
      method => method.serverType === data.paymentType
    );

    const isPaymentFailed = this.isConfirmedNoPay() || this.isPaymentFailed();

    const isPaymentFallenBack = this.isPaymentFallenBack();

    const officesData = cashOffices.data ? cashOffices.data : [];
    const now = appConfigs.data ? moment(appConfigs.data.now) : '';

    const offices = composeOffices(officesData, now);

    return (
      <Segment id={styles.confirmation}>
        <Loader loading={loading} />

        <div className={styles.confirmation_body}>
          <div className={styles.confirmation_title}>
            <img
              src={!isPaymentFailed ? confirmation_icon : fail_icon}
              className={classnames({
                [styles.fail_icon]: isPaymentFailed,
              })}
              alt="icon"
            />
            <p>
              {this.isPaymentFailed()
                ? t(`loan.steps.confirmation.failure_title`)
                : t(`loan.steps.confirmation.title`)}
            </p>
          </div>

          {!loading && (
            <div className={styles.instructions}>
              <div
                className={classnames({
                  [styles.icon_space]: !isPaymentFailed,
                  [styles.warn_icon_space]: isPaymentFailed,
                  [styles.fail_icon_space]: isPaymentFallenBack,
                  [styles.failed_icon_space]: this.isPaymentFailed(),
                })}
              >
                {!this.isPaymentFailed() && (
                  <img
                    src={!isPaymentFailed ? info_icon : warn_icon}
                    className={styles.info_icon}
                    alt="info_icon"
                  />
                )}
              </div>

              {loanTransfer && (
                <div
                  className={classnames({
                    [styles.info_space]: !isPaymentFailed,
                    [styles.warn_info_space]: isPaymentFailed,
                    [styles.fail_info_space]: isPaymentFallenBack,
                    [styles.failed_info_space]: this.isPaymentFailed(),
                  })}
                >
                  <div className={styles.info_text}>
                    {this.infoText(loanTransfer, officesData)}
                  </div>
                </div>
              )}
            </div>
          )}

          {!this.isPaymentFailed() && data.amount && (
            <div className={styles.loan_terms}>
              <div className={styles.loan_data_row}>
                <span className={styles.loan_data}>
                  {t('loan.steps.confirmation.selected_amount')}
                </span>
                <span className={styles.given_loan_data}>
                  <Currency
                    hasSign={true}
                    value={data.amount}
                    signPosition="end"
                  />
                </span>
              </div>

              <div className={styles.loan_data_row}>
                <span className={styles.loan_data}>
                  {t('loan.steps.loan_amount.loan_time')}
                </span>
                <span
                  className={classnames(
                    styles.loan_data,
                    styles.given_loan_data
                  )}
                >
                  {`${data.months} ${t('loan.steps.confirmation.months')}`}
                </span>
              </div>

              <div className={styles.loan_data_row}>
                <span className={styles.loan_data}>
                  {t('loan.steps.loan_amount.payment_value')}
                </span>
                <span
                  className={classnames(
                    styles.loan_data,
                    styles.given_loan_data
                  )}
                >
                  <Currency
                    hasSign={true}
                    value={data.total}
                    signPosition="end"
                  />
                </span>
              </div>

              <div className={styles.loan_data_row}>
                <span className={styles.loan_data}>
                  {t('loan.steps.confirmation.procents')}
                </span>
                <span
                  className={classnames(
                    styles.loan_data,
                    styles.given_loan_data
                  )}
                >
                  <Currency
                    hasSign={true}
                    value={data.total - data.amount}
                    signPosition="end"
                  />
                </span>
              </div>

              <div className={styles.loan_data_row}>
                <span className={styles.loan_data}>
                  {t('loan.steps.loan_amount.payment_day')}
                </span>
                <span
                  className={classnames(
                    styles.loan_data,
                    styles.given_loan_data
                  )}
                >
                  {data.nextPaymentDate &&
                    moment(data.nextPaymentDate).format('DD/MM/YYYY')}
                </span>
              </div>
            </div>
          )}

          {!this.isPaymentFailed() &&
            data.documents &&
            data.documents.map((pdf, index) => {
              return (
                <div key={index} className={styles.contract_field}>
                  <img
                    src={contract_icon}
                    alt="contract"
                    className={styles.contract_icon}
                  />
                  <span
                    className={styles.contract_text}
                    onClick={() => openDocument(pdf.fullPath)}
                  >
                    <span>
                      {t(
                        'loan.steps.confirmation.loan_documents.' +
                          pdf.documentType
                      )}
                    </span>
                    <img
                      src={download_icon}
                      alt="download_icon"
                      className={styles.download_icon}
                    />
                  </span>
                </div>
              );
            })}
        </div>

        {this.shouldDisplay(MAP) && (
          <div className={styles.map}>
            <Map
              google={this.props.google}
              zoom={14}
              initialCenter={{
                lat: 40.214106,
                lng: 44.491185,
              }}
              mapTypeControl={false}
              streetViewControl={false}
            >
              <Marker onClick={this.onMarkerClick} name={'Global Credit'} />

              <InfoWindow onClose={this.onInfoWindowClose}>
                <div>
                  <h1>Global Credit</h1>
                </div>
              </InfoWindow>
            </Map>
          </div>
        )}

        {loanTransfer &&
          loanTransfer.type === 'cash' &&
          this.shouldDisplay(CASH_OFFICES) && (
            <div className={styles.offices_container}>
              {offices &&
                offices.map((office, i) => {
                  return (
                    <div key={i} className={styles.offices_item}>
                      <div className={styles.location_container}>
                        <div className={styles.location_icon}>
                          <SvgComponent
                            name="location"
                            className={this.getOfficeIconClass(office)}
                          />
                        </div>
                      </div>
                      <div>
                        <div className={styles.office_name}>
                          {office.item.cashOfficeType.name}
                        </div>
                        <div>{office.item.address}</div>
                        <div>
                          {t(`loan.steps.confirmation.office_working_hours`)} (
                          {office.schedule})
                        </div>
                        {office.isOpen ? (
                          <div className={styles.office_open}>
                            ({t(`loan.steps.confirmation.office_open`)})
                          </div>
                        ) : (
                          <div className={styles.office_closed}>
                            ({t(`loan.steps.confirmation.office_closed`)})
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })}
            </div>
          )}
      </Segment>
    );
  }
}

function mapStateToProps(state) {
  const { loan, cashOffices, loading, appConfigs } = state;

  return { loan, cashOffices, loading, appConfigs };
}

const mapDispatchToProps = dispatch => {
  return {
    fetchServerTime: () => dispatch(fetchServerTime()),
    fetchLoanByPublicId: id => dispatch(fetchLoanByPublicId(id)),
    fetchCashOfficesByPublicId: id => dispatch(fetchCashOfficesByPublicId(id)),
  };
};

export default compose(
  GoogleApiWrapper({
    apiKey: process.env.MIX_REACT_APP_GOOGLE_MAP_KEY,
  }),
  withRouter,
  withNamespaces('translations'),
  connect(mapStateToProps, mapDispatchToProps)
)(Confirmation);
