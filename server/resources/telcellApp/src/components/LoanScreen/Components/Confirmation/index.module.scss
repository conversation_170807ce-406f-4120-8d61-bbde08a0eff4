@import '../../../../styles/variables';
@import '../../../../styles/sizes';

#confirmation {
  border: none;
  margin: 0;
  padding: 0;

  .confirmation_body {
    margin: 0 auto;
    margin-bottom: 20px;

    .confirmation_title {
      display: flex;
      font-size: 16px;
      color: $black;
      justify-content: center;
      align-items: center;
      padding: 15px 38px 0 5px;

      .fail_icon {
        margin-right: 6px;
      }
    }

    .instructions {
      margin: 15px auto;
      max-width: 889px;
      min-height: 80px;
      display: flex;
      width: 93%;

      .icon_space,
      .warn_icon_space {
        background: $orange;
        width: 83px;
        min-height: 100%;
        border-top-left-radius: 8px;
        border-bottom-left-radius: 8px;
        color: white;
        display: flex;

        .info_icon {
          margin: auto;
          padding: 2px;
        }
      }

      .warn_icon_space {
        background: #89002a;
      }

      .fail_icon_space {
        background: $orange;
      }

      .failed_icon_space {
        background: $main;
      }

      .info_space,
      .warn_info_space {
        background: $main;
        min-height: 100%;
        width: 806px;
        border-top-right-radius: 8px;
        border-bottom-right-radius: 8px;
        color: white;
        display: flex;
        font-size: 18px;

        .info_text {
          width: 100%;
          padding: 10px;
          line-height: 23px;
          display: flex;
          align-items: center;
          text-align: left;
        }
      }

      .warn_info_space {
        background: #c3003c;
      }

      .fail_info_space {
        background: $dark-orange;
      }

      .failed_info_space {
        background: $main;
      }
    }

    .loan_terms {
      max-width: 418px;
      margin: 35px auto;

      .loan_data_row {
        display: flex;
        justify-content: space-between;
        color: $dark-gray;
        margin: 4px 0;
        font-size: 14px;
        font-weight: normal;

        svg {
          height: 11px;
          fill: $light-black;
          stroke-width: 0;
        }

        .given_loan_data {
          color: $light-black;
          text-align: left;
        }
      }
    }

    .documents_loader_container {
      height: 70px;
      text-align: center;

      .documents_loader {
        margin-top: 65px;
      }

      .documents_loading_text {
        color: $secondary;
        font-size: 14px;
      }
    }

    .documents_error_container {
      height: 70px;
      text-align: center;

      .documents_error_text {
        color: $pink;
        font-size: 14px;
      }
    }

    .contract_field {
      margin: 10px auto;
      max-width: 418px;
      display: flex;
      justify-content: flex-start;
      word-break: break-all;
      align-items: center;

      .contract_icon {
        height: 25px;
        width: 18px;
      }

      .contract_text {
        font-size: 14px;
        color: $light-black;
        display: flex;
        justify-content: space-between;
        width: 100%;
        margin-left: 13px;
        align-items: center;
        cursor: pointer;

        .download_icon {
          height: 25px;
          width: 15px;
          fill: $dark-red;
        }
      }
    }
  }

  .ovl_confirmation_text {
    font-size: 14px;

    :global(.toggle-description-btn) {
      color: white;
      text-decoration: underline;
      font-style: italic;
    }

    &.show_more_text_toggled {
      text-align: center;

      :global(.toggle-description-btn) {
        justify-content: center;
        display: flex;
      }
    }
  }

  .offices_container {
    width: 100%;
    margin: 0 auto;
    display: flex;
    flex-wrap: wrap;
    background-color: $white;
    padding: 0;
    max-width: 768px;
    justify-content: center;

    .offices_header {
      text-transform: uppercase;
      font-size: 20px;
      color: $main;
      width: 100%;
      margin: 30px auto;
      text-align: center;
      line-height: 30px;

      .text {
        max-width: 776px;
        margin: 0 auto;
      }
    }

    .office_name {
      display: inline-block;
    }

    .offices_item {
      width: 33%;
      margin: 1px;
      padding: 12px;
      line-height: 24px;
      background-color: $white;
      display: flex;

      .location_container {
        .location_icon {
          margin: 4px 10px 0 0;

          .icon {
            height: 21px;
          }

          .telcell {
            fill: $telcell-orange;
          }

          .global_credit {
            fill: $main;
          }
        }
      }

      .office_open {
        color: $green;
      }

      .office_closed {
        color: $red;
      }

      img {
        width: 16.9px;
      }
    }
  }

  .map {
    margin-top: 20px;
    height: 364px;
    position: relative;
  }

  @media screen and (max-width: $tablet-width) {
    .map {
      height: 260px;
    }

    .offices_container {
      .offices_item {
        width: 49%;
      }
    }
  }

  @media screen and (max-width: $mobile-width) {
    .confirmation_body {
      .contract_field {
        padding: 0 15px;
      }

      .confirmation_title {
        justify-content: flex-start;
      }

      .instructions {
        .info_space,
        .warn_info_space {
          font-size: 14px;
          .info_text {
            width: 100%;
            padding: 10px;
            line-height: 19px;
          }
        }
      }
    }

    .loan_terms {
      padding: 0 15px;
    }

    .offices {
      margin-top: 58px;
      text-align: center;
    }

    .offices_container {
      .offices_item {
        width: 100%;
      }
    }
  }
}
