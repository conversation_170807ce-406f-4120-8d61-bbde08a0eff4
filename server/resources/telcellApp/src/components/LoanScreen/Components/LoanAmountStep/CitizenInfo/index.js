import React, { Component } from 'react';
import { withNamespaces } from 'react-i18next';
import { Grid } from 'semantic-ui-react';
import moment from 'moment';
import classnames from 'classnames';

import RoundedAvatar from '../../../../RoundedAvatar';
import { BIRTH_DATE_FORMAT } from '../../../../../constants';

import styles from './index.module.scss';

class CitizenInfo extends Component {
  fullName = () => {
    const { citizen = {} } = this.props;
    if (this.isCitizenUnavailable()) {
      return;
    }

    return `${citizen.firstName} ${citizen.lastName}`.trim().toLowerCase();
  };

  extractPassportNumber = passports => {
    if (passports) {
      const p =
        passports &&
        (passports.BIOMETRIC_PASSPORT ||
          passports.NON_BIOMETRIC_PASSPORT ||
          passports.ID_CARD);
      return p.passportNumber;
    }
  };

  isCitizenUnavailable = () => {
    return !!this.props.citizen.error;
  };

  render() {
    const { citizen, loading } = this.props;

    const fullNameClasses = classnames(styles.user_name_new, {
      [styles.user_name_hide]: loading,
    });

    return (
      <div className={styles.user_info}>
        <Grid>
          <Grid.Row columns={2}>
            <Grid.Column width={3}>
              <RoundedAvatar
                size="xs"
                loading={loading}
                photo={citizen.photo}
                firstName={citizen.firstName}
              />
            </Grid.Column>
            <Grid.Column width={13} className={styles.user_info_section}>
              <div className={fullNameClasses}>
                <span className={fullNameClasses}>{this.fullName()}</span>
              </div>
              <div className={styles.passpoprt_data}>
                <span className={styles.user_data}>
                  {this.extractPassportNumber(citizen.passports)}
                </span>
                <span className={styles.divider}>/</span>
                <span className={styles.user_data}>
                  {citizen.birthDate &&
                    moment(citizen.birthDate).format(BIRTH_DATE_FORMAT)}
                </span>
              </div>
            </Grid.Column>
          </Grid.Row>
        </Grid>
      </div>
    );
  }
}

export default withNamespaces('translations')(CitizenInfo);
