@import '../../../../../styles/variables';
@import '../../../../../styles/sizes';

.user_info {
  padding: 0 38% 0 38%;
  text-align: center;
  height: auto;
  width: 100%;
  color: $dark-gray;
  background-color: $light-gray;
  margin: 15px 0;

  .user_info_section {
    display: flex !important;
    flex-direction: column;
    justify-content: center;
  }

  .passpoprt_data {
    padding: 0;
    text-align: left;

    .user_info_column {
      box-shadow: none !important;
    }

    .divider {
      color: $main;
      margin: 0 5px;
      font-size: 22px;
      position: relative;
      top: 2px;
    }
  }

  .user_name {
    font-size: 24px;
    color: $main;
    margin-top: 14px;
    text-transform: capitalize;
    margin-bottom: 10px;

    .user_name_hide {
      visibility: hidden;
    }
  }

  .user_name_new {
    font-size: 18px;
    color: $black;
    text-transform: capitalize;
    margin-bottom: 3px;
    text-align: left;
    font-weight: bold;

    .user_name_hide {
      visibility: hidden;
    }
  }

  .vehicle_info {
    margin-top: 5px;
  }

  .user_datas {
    max-width: 310px;
    margin: 15px auto 52px;

    .user_data_row {
      display: flex;
      justify-content: space-between;

      .user_data {
        margin: 5px 0;
        font-size: 16px;
        color: $main-text-color;
      }

      .user_data:last-child {
        text-transform: uppercase;
      }
    }
  }
}

@media screen and (max-width: $laptop-width) {
  .user_info {
    padding: 0 35% 0 35%;
  }
}

@media screen and (max-width: $tablet-width) {
  .user_info {
    padding: 0 29% 0 29%;
  }
}

@media screen and (max-width: $lowers-tablet-width) {
  .user_info {
    padding: 0 18% 0 18%;
  }
}

@media screen and (max-width: $mobile-width) {
  .user_info {
    padding: 0 4% 0 4%;

    .user_name {
      font-size: 14px;
    }

    .user_datas {
      max-width: 241px;

      .user_data_row {
        .user_data {
          margin: 3px 0;
          font-size: 12px;
        }
      }
    }

    .passpoprt_data {
      .user_info_column {
        padding: 0 5px !important;

        .user_data {
          font-size: 12px;
        }
      }
    }
  }
}
