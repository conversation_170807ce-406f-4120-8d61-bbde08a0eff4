import React, { Component } from 'react';
import { withNamespaces } from 'react-i18next';
import ReCAPTCHA from 'react-google-recaptcha';
import PropTypes from 'prop-types';
import classnames from 'classnames';
import moment from 'moment';

import GCButton from '../../../../GCButton';
import Navigation from '../../../../CreditWizard/Navigation';
import Validation from '../../../../Validation';
import { RECAPTCHA_KEY } from '../../../../../config/';
import Currency from '../../../../Currency';
import AprApproval from '../../../../AprApproval';
import { FRACTION_NO } from '../../../../../constants';
import SkeletonLoader from './SkeletonLoader';

import styles from './index.module.scss';

class LoanInfo extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  goHome = () => {
    const { history } = this.props;

    history.push({
      pathname: '/',
    });
  };

  render() {
    const {
      t,
      step,
      amount,
      monthlyPayment,
      serviceFeeRate,
      withdrawalFeeAmount,
      loading,
      setFieldValue,
      isLoanUnavailable,
      loanName,
      citizen,
      schedule,
    } = this.props;

    const name = `${loanName}AprApprovalCheckbox`;
    const qrDiscount = +citizen.qrDiscount;

    return (
      <>
        <div className={styles.loan_info}>
          {loading ? (
            <SkeletonLoader count={9} />
          ) : (
            <div className={styles.loan_datas}>
              <div className={styles.loan_data_row}>
                <span className={styles.loan_data}>
                  {t('loan.steps.loan_amount.loan_value')}
                </span>
                <span className={styles.given_loan_data_price}>
                  <Currency
                    hasSign={true}
                    value={amount}
                    precision={FRACTION_NO}
                    signPosition="end"
                  />
                </span>
              </div>
              <div className={styles.loan_data_row}>
                <span className={styles.loan_data}>
                  {t('loan.steps.loan_amount.interest_rate')}
                </span>
                <span
                  className={classnames(
                    styles.loan_data,
                    styles.given_loan_data
                  )}
                >
                  {`${schedule.apr} %`}
                </span>
              </div>
              {!!qrDiscount && (
                <div
                  className={classnames(styles.loan_data_row, styles.loan_rate)}
                >
                  <div
                    className={classnames(
                      styles.given_loan_data,
                      styles.loan_rate_struck_through
                    )}
                  >
                    {`${serviceFeeRate + qrDiscount} %`}
                  </div>
                </div>
              )}
              <div className={styles.loan_data_row}>
                <span className={styles.loan_data}>
                  {t('loan.steps.loan_amount.annual_rate')}
                </span>
                {!!qrDiscount ? (
                  <span>
                    <span
                      className={classnames(
                        styles.loan_rate_font,
                        styles.given_loan_data_price
                      )}
                    >
                      {`(-${qrDiscount} %)  `}
                    </span>
                    <span className={styles.given_loan_data_price}>
                      {`${serviceFeeRate} %`}
                    </span>
                  </span>
                ) : (
                  <span
                    className={classnames(
                      styles.loan_data,
                      styles.given_loan_data
                    )}
                  >
                    {`${serviceFeeRate} %`}
                  </span>
                )}
              </div>
              <div className={styles.loan_data_row}>
                <span className={styles.loan_data}>
                  {t('loan.steps.loan_amount.loan_time')}
                </span>
                <span
                  className={classnames(
                    styles.loan_data,
                    styles.given_loan_data
                  )}
                >
                  {`${schedule.months} ${t('loan.steps.loan_amount.months')}`}
                </span>
              </div>
              <div className={styles.loan_data_row}>
                <span className={styles.loan_data}>
                  {t('loan.steps.loan_amount.monthly_payment')}
                </span>
                <span
                  className={classnames(
                    styles.loan_data,
                    styles.given_loan_data
                  )}
                >
                  <Currency
                    hasSign={true}
                    value={monthlyPayment}
                    signPosition="end"
                  />
                </span>
              </div>
              <div className={styles.loan_data_row}>
                <span className={styles.loan_data}>
                  {t('loan.steps.loan_amount.last_month_payment')}
                </span>
                <span
                  className={classnames(
                    styles.loan_data,
                    styles.given_loan_data
                  )}
                >
                  <Currency
                    hasSign={true}
                    value={schedule.lastMonthPayment}
                    signPosition="end"
                  />
                </span>
              </div>
              {!!withdrawalFeeAmount && (
                <div className={styles.loan_data_row}>
                  <span className={styles.loan_data}>
                    {t('loan.steps.loan_amount.withdrawal_fee')}
                  </span>
                  <span
                    className={classnames(
                      styles.loan_data,
                      styles.given_loan_data
                    )}
                  >
                    <Currency
                      hasSign={true}
                      value={withdrawalFeeAmount}
                      signPosition="end"
                    />
                  </span>
                </div>
              )}
              <div className={styles.loan_data_row}>
                <span className={styles.loan_data}>
                  {t('loan.steps.loan_amount.payment_value')}
                </span>
                <span
                  className={classnames(
                    styles.loan_data,
                    styles.given_loan_data
                  )}
                >
                  <Currency
                    hasSign={true}
                    value={String(schedule.total + withdrawalFeeAmount)}
                    signPosition="end"
                  />
                </span>
              </div>
              <div className={styles.loan_data_row}>
                <span className={styles.loan_data}>
                  {t('loan.steps.loan_amount.payment_day')}
                </span>
                <span
                  className={classnames(
                    styles.loan_data,
                    styles.given_loan_data
                  )}
                >
                  {schedule.nextPaymentDate &&
                    moment(schedule.nextPaymentDate.date).format('DD/MM/YYYY')}
                </span>
              </div>
            </div>
          )}
        </div>

        <AprApproval apr={schedule.apr} name={name} />

        <div className={styles.recaptcha}>
          <Validation name="isHuman" showMessage={true}>
            <ReCAPTCHA
              sitekey={RECAPTCHA_KEY}
              render="explicit"
              className={styles.recaptcha_checkbox}
              onChange={() => {
                setFieldValue('isHuman', true);
              }}
            />
          </Validation>
        </div>
        <Navigation>
          {!loading && isLoanUnavailable() ? (
            <GCButton
              type="button"
              onClick={this.goHome}
              className={styles.previous_step_button}
              disabled={loading}
            >
              {t(step.previous)}
            </GCButton>
          ) : (
            <GCButton
              type="submit"
              primary={true}
              className={styles.next_step_button}
              arrowIcon={true}
              disabled={loading}
            >
              {t(step.next)}
            </GCButton>
          )}
        </Navigation>
      </>
    );
  }
}

LoanInfo.propTypes = {
  onChange: PropTypes.func,
};

LoanInfo.defaultProps = {
  onChange: () => {},
};

export default withNamespaces('translations')(LoanInfo);
