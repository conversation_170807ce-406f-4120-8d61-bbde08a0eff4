@import '../../../../../styles/variables';
@import '../../../../../styles/sizes';

.loan_info {
  border-top: 1px solid $gray;

  .loan_datas {
    width: 100%;

    .loan_data_row {
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid $gray;
      padding: 10px 0;

      svg {
        height: 10px;
        stroke: none;
      }

      .given_loan_data_price {
        color: $main;
        margin: 4px 0;
        font-size: 12px;

        svg {
          fill: $main;
        }
      }

      .loan_data,
      .given_loan_data {
        margin: 4px 0;
        font-size: 12px;
        color: $black;

        svg {
          fill: $black;
        }
      }

      .loan_rate_font {
        font-size: 12px;
      }

      .loan_rate_struck_through {
        @extend .loan_rate_font;
        position: absolute;
        right: 1px;
        margin-top: 0px;
        text-decoration: line-through;
      }
    }

    .loan_rate {
      position: relative;
      height: 15px;
    }
  }
}

.recaptcha {
  margin: 20px auto;
  display: inline-flex;
  width: 100%;
  justify-content: center;
  flex-direction: column;
  text-align: center;

  .recaptcha_checkbox {
    margin: 0 auto;
  }
}

@media screen and (max-width: 350px) {
  .recaptcha_checkbox {
    transform: scale(0.9) translateX(-50%);
    transform-origin: 0 0;
    position: relative;
    left: 50%;
  }
}
