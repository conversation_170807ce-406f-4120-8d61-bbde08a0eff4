@import '../../../../../styles/variables';
@import '../../../../../styles/sizes';

.loan_content {
  max-width: 600px;
  margin: 0 auto;
  background-color: $white;
  border-top-left-radius: 35px;
  border-top-right-radius: 35px;
  padding: 0 20px;

  .loan_amount {
    padding-top: 15px;

    .loan_info_title {
      font-size: 18px;
      padding-bottom: 20px;
      border-bottom: 1px solid $gray;
    }
  }

  .loan_amount_info_section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid $gray;
    padding: 20px 0;

    .label {
      font-size: 14px;
      max-width: 200px;
    }

    .value {
      font-size: 18px;
      font-weight: bold;

      svg {
        height: 13px !important;
        width: 20px;
      }
    }
  }
}
