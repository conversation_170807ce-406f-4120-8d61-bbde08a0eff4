import React, { Component } from 'react';
import { withNamespaces } from 'react-i18next';
import PropTypes from 'prop-types';
import classnames from 'classnames';
import { Radio } from 'semantic-ui-react';

import VehicleInfoModal from '../VehicleInfoModal';

import styles from './index.module.scss';

class VehicleInfo extends Component {
  constructor(props) {
    super(props);

    this.state = {
      showVehicleModal: false,
    };
  }

  componentDidUpdate(prevProps) {
    const { changeCurrentOvl, citizen } = this.props;

    const tradeVehicle = this.getTradeVehicle();

    if (
      !citizen.tradeError &&
      citizen.data &&
      prevProps.citizen.data !== citizen.data &&
      tradeVehicle &&
      !tradeVehicle.rejected
    ) {
      this.setState({
        showVehicleModal: false,
      });
      changeCurrentOvl(tradeVehicle);
    }
  }

  toggleVehicleModal = () => {
    this.setState({
      showVehicleModal: !this.state.showVehicleModal,
    });
  };

  hideVehicleInput = () => {
    this.setState({
      showVehicleModal: false,
    });
  };

  getTradeVehicle = () => {
    const { citizen } = this.props;
    const credit = citizen.data.credit;

    return (
      credit &&
      credit.find(p => {
        return p.vehicleInfo && p.vehicleInfo.tradeAmount !== null;
      })
    );
  };

  isRadioChecked = (currentOvl, credit) => {
    return (
      currentOvl && currentOvl.vehicleInfo.number === credit.vehicleInfo.number
    );
  };

  render() {
    const {
      t,
      citizen,
      citizen: { data: citizenData = {} },
      currentOvl,
      handleRadioChange,
      isLoanUnavailable,
      formik,
      scheduleLoading,
    } = this.props;

    const OvlCredits =
      citizenData.credit &&
      citizenData.credit.filter(credit => !credit.rejected);

    const { showVehicleModal } = this.state;

    return (
      <div
        className={classnames({
          [styles.vehicle_section]: true,
        })}
      >
        <div className={styles.vehicle_titles_section}>
          <span className={styles.pledge}>
            {t('loan.steps.loan_amount.pledge_vehicle_title')}
          </span>
          {citizenData.allowTrade && (
            <span
              className={classnames({
                [styles.new_vehicle_button]: citizenData.allowTrade,
                [styles.new_vehicle_button_disabled]: scheduleLoading,
              })}
              onClick={!scheduleLoading ? this.toggleVehicleModal : undefined}
            >
              {t('loan.steps.trade_vehicle.buttons.own_vehicle')}
            </span>
          )}
        </div>
        <div className={styles.vehicle_pledge_section}>
          {OvlCredits &&
            OvlCredits.map((credit, index) => {
              return (
                <div key={index} className={styles.vehicle_container}>
                  <div className={styles.vehicle_info_section}>
                    <Radio
                      name={'vehicle'}
                      value={credit.vehicleInfo.number}
                      checked={this.isRadioChecked(currentOvl, credit)}
                      className={styles.vehicle_radio}
                      onChange={(e, { value }) => {
                        handleRadioChange(value);
                      }}
                      disabled={scheduleLoading}
                    />
                    <div className={styles.vehicle_flag_label}>
                      <div className={styles.flag}>
                        <div className={styles.flag_red} />
                        <div className={styles.flag_blue} />
                        <div className={styles.flag_orange} />
                      </div>
                      <div className={styles.text}>AM</div>
                    </div>
                    <div className={styles.seperator} />
                    <div className={styles.vehicle_info_container}>
                      <div className={styles.vehicle_number}>
                        {credit.vehicleInfo.number}
                      </div>
                      <div className={styles.vehicle_details}>
                        {credit.vehicleInfo.model},{' '}
                        {credit.vehicleInfo.released}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
        </div>
        {citizenData.allowTrade && (
          <div className={styles.vehicle_pledge_section}>
            <div>
              {isLoanUnavailable() && (
                <div className={styles.loan_only_trade}>
                  {t('loan.steps.loan_amount.loan_unavailable')}
                </div>
              )}
            </div>
            <div className={styles.new_vehicle_modal}>
              <VehicleInfoModal
                {...this.props}
                formik={formik}
                open={showVehicleModal}
                closeModal={this.hideVehicleInput}
                tradeError={citizen.tradeError}
                getTradeVehicle={this.getTradeVehicle}
              />
            </div>
          </div>
        )}
      </div>
    );
  }
}

VehicleInfo.propTypes = {
  onChange: PropTypes.func,
};

VehicleInfo.defaultProps = {
  onChange: () => {},
};

export default withNamespaces('translations')(VehicleInfo);
