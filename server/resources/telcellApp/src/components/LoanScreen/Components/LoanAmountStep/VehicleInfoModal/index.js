import React, { Component } from 'react';
import { compose } from 'redux';
import { connect } from 'react-redux';
import { Image, Modal, Input } from 'semantic-ui-react';
import styles from './index.module.scss';
import { withNamespaces } from 'react-i18next';
import { Formik } from 'formik';
import NumberFormat from 'react-number-format';
import classnames from 'classnames';

import Validation from '../../../../Validation';
import ServerError from '../../../../Validation/ServerError';
import ErrorFocus from '../../../../Validation/ErrorFocus';
import { tradeVehicleSchema } from '../../../../../validation/schemas/wizardLoanSchema';
import VehicleNumberInput from '../../../../VehicleNumberInput';
import VehicleTechPassportInput from '../../../../VehicleTechPassportInput';
import GCButton from '../../../../GCButton';
import Loader from '../../../../Loader';

import { fetchTradeVehicle } from '../../../../../store/ducks/citizen';

import cancel from '../../../../../images/cancel.svg';
import SvgComponent from '../../../../SvgComponent';

class VehicleInfoModal extends Component {
  constructor(props) {
    super(props);
  }

  handleTradeVehicle = payload => {
    const { fetchTradeVehicle } = this.props;
    const { vehicleNumber, techPassport, tradeAmount } = payload;

    fetchTradeVehicle({
      vehicleNumber: vehicleNumber.replace(/,/g, ''),
      techPassport: techPassport.replace(/,/g, ''),
      tradeAmount: tradeAmount.toString().replace(/,/g, ''),
    });
  };

  handleVehiclePassportChange = (e, subFormikSetFieldValue) => {
    const value = e.target.value.replace(/\s/g, '').toUpperCase();

    subFormikSetFieldValue('techPassport', value);
  };

  handleVehicleNumberChange = (e, subFormikSetFieldValue) => {
    const value = e.target.value.replace(/\s/g, '').toUpperCase();

    subFormikSetFieldValue('vehicleNumber', value);
  };

  handleInputChange = (e, subFormikSetFieldValue) => {
    const value = e.floatValue;

    subFormikSetFieldValue('tradeAmount', value);
  };

  getInitialValues = () => {
    return {
      vehicleNumber: '',
      techPassport: '',
      tradeAmount: '',
    };
  };

  render() {
    const {
      t,
      open,
      formik,
      tradeError,
      closeModal,
      citizen: { tradeLoading },
    } = this.props;

    return (
      <Modal
        centered={false}
        dimmer="inverted"
        id={styles.vehicle_info_modal}
        open={open}
        onClose={closeModal}
        closeIcon={<Image className={styles.cancel} src={cancel} />}
      >
        <Modal.Content image>
          <Formik
            ref={ref => (this.formikRef = ref)}
            initialValues={this.getInitialValues()}
            onSubmit={this.handleTradeVehicle}
            validationSchema={tradeVehicleSchema}
            validateOnBlur={true}
            validateOnChange={false}
          >
            {subFormik => {
              if (formik.isValidating) {
                formik.isValidating = false;
              }
              return (
                <div className={styles.modal_container}>
                  <div className={styles.vehicle_container}>
                    <div className={styles.vehicle_info_title}>
                      {t('loan.steps.trade_vehicle.info_text')}
                    </div>
                    <div className={styles.vehicle_info_section}>
                      <Validation name="vehicleNumber" showMessage={false}>
                        <div
                          className={classnames(
                            styles.vehicle_number_container,
                            'vehicle_input_container'
                          )}
                        >
                          <VehicleNumberInput
                            {...subFormik}
                            handleChange={e => {
                              this.handleVehicleNumberChange(
                                e,
                                subFormik.setFieldValue
                              );
                            }}
                          />
                        </div>
                        <div className={styles.vehicle_text}>
                          {t('loan.steps.trade_vehicle.number')}
                        </div>
                      </Validation>
                    </div>

                    <div className={styles.vehicle_info_section}>
                      <Validation name="techPassport" showMessage={false}>
                        <div
                          className={classnames(
                            styles.tech_passport_container,
                            'vehicle_input_container'
                          )}
                        >
                          <VehicleTechPassportInput
                            {...subFormik}
                            handleChange={e => {
                              this.handleVehiclePassportChange(
                                e,
                                subFormik.setFieldValue
                              );
                            }}
                          />
                        </div>
                        <div className={styles.vehicle_text}>
                          {t('loan.steps.trade_vehicle.passport')}
                        </div>
                      </Validation>
                    </div>

                    <div className={styles.vehicle_info_section}>
                      <Validation name="tradeAmount" showMessage={false}>
                        <div
                          className={classnames(
                            styles.trade_amount_container,
                            'vehicle_input_container'
                          )}
                        >
                          <div className={styles.currency}>
                            <NumberFormat
                              customInput={Input}
                              onValueChange={values => {
                                this.handleInputChange(
                                  values,
                                  subFormik.setFieldValue
                                );
                              }}
                              value={subFormik.values.tradeAmount}
                              id="tradeAmount"
                              placeholder="1,000,000"
                              autoComplete="off"
                              type="tel"
                              labelPosition="left"
                              allowNegative={false}
                              thousandSeparator={true}
                              maxLength={12}
                              className={styles.currency_input}
                              label={{
                                basic: true,
                                content: (
                                  <div
                                    className={
                                      styles.vehicle_number_input_label
                                    }
                                  >
                                    <div className={styles.label_container}>
                                      <SvgComponent
                                        className={styles.dram_icon}
                                        name="amd"
                                      />
                                    </div>
                                    <div
                                      className={
                                        styles.vehicle_input_label_seperator
                                      }
                                    />
                                  </div>
                                ),
                              }}
                            />
                          </div>
                        </div>
                        <div className={styles.vehicle_text}>
                          {t('loan.steps.trade_vehicle.amount')}
                        </div>
                      </Validation>
                    </div>
                    <div className={styles.vehicle_info_error}>
                      {tradeError && <ServerError error={tradeError} />}
                    </div>
                  </div>

                  <Loader loading={tradeLoading} circleLoader={true} />

                  <div>
                    <div className={styles.vehicle_info_button_add}>
                      <GCButton primary={true} onClick={subFormik.handleSubmit}>
                        {t('loan.steps.trade_vehicle.add')}
                      </GCButton>
                    </div>
                    <div
                      className={classnames(styles.vehicle_info_button_cancel)}
                    >
                      <GCButton primary={true} onClick={() => closeModal()}>
                        {t('loan.steps.trade_vehicle.cancel')}
                      </GCButton>
                    </div>
                  </div>

                  <ErrorFocus />
                </div>
              );
            }}
          </Formik>
        </Modal.Content>
      </Modal>
    );
  }
}

const mapDispatchToProps = dispatch => {
  return {
    fetchTradeVehicle: trade_vehicle =>
      dispatch(fetchTradeVehicle(trade_vehicle)),
  };
};

export default compose(
  withNamespaces('translations'),
  connect(null, mapDispatchToProps)
)(VehicleInfoModal);
