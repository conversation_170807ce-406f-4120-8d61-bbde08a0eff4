@import '../../../../../styles/variables';
@import '../../../../../styles/sizes';

#vehicle_info_modal {
  width: 40%;
  height: max-content;
  color: $wet-asphalt;
  padding: 25px 20px;
  border-top-left-radius: 35px;
  border-top-right-radius: 35px;

  .cancel {
    position: absolute;
    cursor: pointer;
    right: 30px;
    width: 15px;
    height: 15px;
    margin: 0;
  }

  .vehicle_info_title {
    font-size: 18px;
    color: $black;
    text-transform: uppercase;
    padding-bottom: 10px;
    border-bottom: 2px solid $gray;
    margin-bottom: 20px;
  }

  .modal_container {
    width: 100%;
  }

  .vehicle_container {
    max-width: 100%;
    text-align: left;
    background-color: $white;

    .vehicle_info_error {
      padding-bottom: 12px;
      height: 30px;

      :global(.server_error) {
        margin: 0;
      }
    }

    :global .vehicle_input_container {
      border-bottom: 1px solid $gray;
      padding: 10px 0;
    }

    .vehicle_text {
      font-size: 11px;
      font-family: $main-font-family;
      font-style: oblique;
      color: $secondary;
      padding-bottom: 10px;
      padding-top: 3px;
      text-align: right;
    }

    .trade_amount_container {
      margin: 0 auto;

      .currency {
        display: flex;
        flex-direction: column;

        input {
          width: 220px;
          font-size: 22px;
          font-weight: normal;
          height: 36px;
          border: none !important;
          font-family: $main-font-family;
          padding-left: 30px;
          padding-right: 5px;
        }

        input:focus {
          outline: none;
          border: none;
        }

        .dram_icon {
          width: 20px;
          height: 21px;
          fill: $main;
          margin-top: 7px;
          margin-left: 5px;
        }

        .currency_input {
          width: 265px;

          div:first-child {
            width: 45px;
            padding: 0 12px 0 0;
            border: none;
            position: relative;
          }
        }

        .vehicle_number_input_label {
          display: inline-flex;
          margin-left: 12px;
          height: 36px;

          .label_container {
            padding-top: 2.5px;
            padding-bottom: 7.5px;
          }

          .vehicle_number_input_label_flag {
            margin: 5px 0 0 0;
          }

          .vehicle_number_input_label_text {
            margin-top: 3px;
          }

          .vehicle_input_label_seperator {
            margin-top: 3px;
            height: 30px;
            margin-left: 8px;
            border-left: 2px solid $light-gray;
          }
        }

        .currency_text {
          display: block;
          font-size: 12px;
          font-style: italic;
          margin-left: 10px;
        }
      }
    }

    .vehicle_amount_section {
      width: 100%;
      padding: 20px 0 13px 17px;
      background-color: $main;
    }
  }

  .vehicle_info_button_add {
    button {
      background-color: $main;
    }
  }

  .vehicle_info_button_cancel {
    button {
      background-color: transparent;
      border: 1px solid $main;
      margin-top: 10px;
      color: $main;
    }
  }

  @media screen and (max-width: $laptop-width) {
    width: 80%;
  }

  @media screen and (max-width: $lowers-tablet-width) {
    width: 100%;
    overflow: hidden;
    padding-bottom: 0;

    .modal_container {
      height: 100vh;
      min-height: unset;
    }
  }
}
