@import '../../../../styles/variables';
@import '../../../../styles/sizes';

#loan_amount_form {
  .available_amount_text {
    text-align: center;
    max-width: 300px;
    text-transform: uppercase;
    margin: 0 auto;
    font-size: 20px;
    line-height: 22px;
    color: $black;
  }
}

.user_info {
  padding: 0 38%;
  text-align: center;
  height: auto;
  width: 100%;
  color: #707070;
  background-color: $white;

  .passpoprt_data {
    padding: 0;
  }

  .user_name {
    font-size: 24px;
    color: $main;
    margin-top: 14px;
    text-transform: capitalize;
    margin-bottom: 10px;

    .user_name_hide {
      visibility: hidden;
    }
  }

  .user_name_new {
    font-size: 18px;
    color: $main;
    text-transform: capitalize;
    margin-bottom: 10px;
    text-align: left;

    .user_name_hide {
      visibility: hidden;
    }
  }

  .vehicle_info {
    margin-top: 5px;
  }

  .user_datas {
    max-width: 310px;
    margin: 15px auto 52px;

    .user_data_row {
      display: flex;
      justify-content: space-between;

      .user_data {
        margin: 5px 0;
        font-size: 16px;
        color: $main-text-color;
      }

      .user_data:last-child {
        text-transform: uppercase;
      }
    }
  }

  .user_datas_oasl {
    max-width: 350px;

    .user_data_row {
      .user_data:last-child {
        word-break: break-all;
        max-width: 70%;
      }
    }
  }
}

.available_loan {
  display: flex;
  justify-content: center;
  flex-direction: row;
  height: 52px;
  line-height: 52px;
  padding: 0 0.5em;
  margin-bottom: 15px;

  .available_loan_text {
    font-size: 17px;
    color: $main;
    text-align: center;
    margin-right: 19px;
  }

  .available_loan_price {
    height: 100%;
    width: 210px;
    font-size: 18px;
    font-weight: bold;
    text-align: center;
    line-height: 52px;
    color: $white;
    background-color: $orange;
    display: flex;
    flex-direction: row;
    justify-content: center;

    .available_price_value {
      & > div {
        font-family: 'Arial';
        font-size: 30px;

        svg {
          height: 23px;
        }
      }
    }
  }
}

.loan_info {
  border-top: 1px solid $gray;
  margin: 30px auto;
  padding: 17px 30px 0 30px;
  width: 418px;
  height: 235px;
  align-items: center;

  .loan_datas {
    width: 100%;

    .loan_data_row {
      display: flex;
      justify-content: space-between;

      svg {
        height: 11px;
      }

      .given_loan_data_price {
        color: $main;
        margin: 4px 0;
        font-size: 14px;

        svg {
          fill: $main;
        }
      }

      .given_loan_data,
      .given_loan_data_price {
        font-weight: bold;
      }

      .loan_data,
      .given_loan_data {
        margin: 4px 0;
        font-size: 14px;
        color: $main-text-color;

        svg {
          fill: $main-text-color;
        }
      }

      .loan_rate_font {
        font-size: 11px;
        font-weight: 500;
      }

      .loan_rate_struck_through {
        @extend .loan_rate_font;
        position: absolute;
        right: 1px;
        margin-top: 0px;
        text-decoration: line-through;
      }
    }

    .loan_rate {
      position: relative;
      height: 15px;
    }
  }
}

.recaptcha {
  margin: 20px auto;
  display: inline-flex;
  width: 100%;
  justify-content: center;
  flex-direction: column;
  text-align: center;

  .recaptcha_checkbox {
    margin: 0 auto;
  }
}

.next_step_button {
  background: $web-orange;
}

.loan_rejection_error {
  padding: 0 25px;
  text-align: center;
  color: $pink;
  font-size: 17px;
  max-width: 420px;
  margin: 70px auto 59px;
  line-height: 1.3;
}

@media screen and (max-width: $laptop-width) {
  .user_info {
    padding: 1% 35%;
  }
}

@media screen and (max-width: $tablet-width) {
  .user_info {
    padding: 0 29%;
  }
}

@media screen and (max-width: $lowers-tablet-width) {
  .user_info {
    padding: 0 18%;
  }

  .available_loan {
    height: 40px;
    line-height: 40px;

    .available_loan_text {
      font-size: 14px;
      margin-right: 10px;
    }

    .available_loan_price {
      width: 180px;
      line-height: 40px;
      font-size: 12px;

      .available_price_dram {
        height: 18px;
      }

      .available_price_value {
        & > div {
          font-size: 25px;

          svg {
            height: 18px;
            margin-right: 0px;
          }
        }
      }
    }
  }
}

@media screen and (max-width: $mobile-width) {
  .loan_header {
    .active_step_title {
      font-size: 19px;
    }
  }

  .user_info {
    padding: 0 4%;

    .user_name {
      font-size: 14px;
    }

    .user_datas {
      max-width: 241px;

      .user_data_row {
        .user_data {
          margin: 3px 0;
          font-size: 12px;
        }
      }
    }

    .passpoprt_data {
      .user_info_column {
        padding: 0 5px !important;

        .user_data {
          font-size: 12px;
        }
      }
    }
  }

  .loan_info {
    border-top: 1px solid $gray;
    width: 100%;

    .loan_datas {
      .loan_data_row {
        .given_loan_data_price {
          font-size: 10px;
        }

        .given_loan_data,
        .given_loan_data_price {
          font-weight: bold;
        }

        .loan_data,
        .given_loan_data {
          font-size: 10px;
        }

        .loan_rate_font {
          font-size: 8px;
        }
      }
    }
  }

  .available_loan {
    flex-direction: column;
    height: inherit;
    line-height: initial;

    .available_loan_text {
      font-size: 17px;
      margin-bottom: 22px;
    }

    .available_loan_price {
      margin: auto;
      height: 52px;
      width: 241px;
      line-height: 52px;
      font-size: 18px;

      .available_price_value {
        & > div {
          font-size: 30px;

          svg {
            height: 23px;
          }
        }
      }
    }

    @supports (-webkit-overflow-scrolling: touch) {
      .available_loan_price {
        .available_price_dram {
          height: 14px;
        }

        .available_price_value {
          font-size: 20px;
        }
      }
    }
  }
}
