import React from 'react';
import _ from 'lodash';
import { withNamespaces } from 'react-i18next';

import SvgComponent from '../../../SvgComponent';

import styles from './index.module.scss';

const LoanRejection = ({ t }) => {
  return (
    <div className={styles.loan_rejection_text}>
      <div className={styles.header}>
        <SvgComponent name="fail-icon" />
        <p>{t('loan.steps.loan_amount.rejection_text')}</p>
      </div>
      <div className={styles.desc}>
        <div className={styles.title}>
          {t('loan.steps.loan_amount.rejection_text_reason')}
        </div>
        <ul>
          {_.range(0, 4).map((el, index) => (
            <li key={index}>
              <SvgComponent name="fail-icon" className={styles.list_icon} />
              <span>{t(`loan.steps.loan_amount.rejection_text_${el}`)}</span>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default withNamespaces('translations')(LoanRejection);
