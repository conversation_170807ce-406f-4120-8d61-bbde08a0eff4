import React, { Component } from 'react';
import { withNamespaces } from 'react-i18next';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { Checkbox, Dropdown, Input } from 'semantic-ui-react';
import PropTypes from 'prop-types';
import classnames from 'classnames';
import { Formik, Form } from 'formik';
import moment from 'moment';

import wizardPersonalDataSchema from '../../../../validation/schemas/wizardPersonalDataSchema';
import Validation from '../../../Validation';
import Navigation from '../../../CreditWizard/Navigation';
import {
  NOTIFICATION_METHODS,
  DISPUTE_SOLUTION,
  BIRTH_DATE_FORMAT,
  PHONE_NUMBER_MASK,
} from '../../../../constants';
import ErrorFocus from '../../../Validation/ErrorFocus';
import Loader from '../../../Loader';
import PhoneNumberInput from '../../../PhoneNumberInput';
import CitizenInfo from '../LoanAmountStep/CitizenInfo';
import { handlePreventEnterKey } from '../../../../helpers/input';

import {
  getPersonalInfo,
  updatePersonalInfo,
} from '../../../../store/ducks/loan';

import styles from './index.module.scss';

class PersonalInfoStep extends Component {
  constructor(props) {
    super(props);

    this.state = {
      email: '',
      phoneNumber: '',
      additionalPhoneNumber: '',
      firstName: '',
      lastName: '',
      amount: 0,
      apartment: '',
      birthDate: '',
      building: '',
      citizenship: '',
      community: '',
      duration: 0,
      photo: '',
      region: '',
      socCardNumber: '',
      street: '',
      city: '',
      notificationMethod: NOTIFICATION_METHODS[0].value,
      disputeSolutionMethod: DISPUTE_SOLUTION[0].value,
    };
  }

  componentDidMount() {
    window.scroll(0, 0);

    this.props.getPersonalInfo();
  }

  componentDidUpdate(prevProps) {
    const {
      loan: {
        data: { citizen },
      },
    } = this.props;

    if (prevProps.loan.data.citizen !== citizen) {
      this.setState({
        ...citizen,
        socCardNumber: citizen.passports.SOC_CARD.passportNumber,
      });
    }
  }

  handleSubmit = payload => {
    const {
      wizardBag: { goNext },
      updatePersonalInfo,
    } = this.props;

    this.setState({
      ...payload,
    });

    updatePersonalInfo(payload, goNext);
  };

  handleChange = (e, setFieldValue) => {
    const value = e.target.value.replace(/\s/g, '');

    setFieldValue('additionalPhoneNumber', value.replace(/[()]/g, ''));
  };

  composeAddress() {
    return `${this.state.street || ''} ${this.state.building || ''} ${this.state
      .apartment || ''}`.trim();
  }

  isLoading = () => {
    const { loan } = this.props;
    return loan.loading;
  };

  getAdditionalPhoneNumber() {
    const { additionalPhoneNumber } = this.state;

    return additionalPhoneNumber && additionalPhoneNumber.includes('+374')
      ? additionalPhoneNumber.slice(4)
      : additionalPhoneNumber;
  }

  getInitialValues = () => {
    const { loan } = this.props;
    const { email, notificationMethod, disputeSolutionMethod } = this.state;

    return {
      isChecked: false,
      notificationMethod: loan.data.notificationMethod || notificationMethod,
      disputeSolutionMethod:
        loan.data.disputeSolutionMethod || disputeSolutionMethod,
      additionalPhoneNumber: this.getAdditionalPhoneNumber() || '',
      email: email || '',
    };
  };

  render() {
    const { t, wizardBag, loan } = this.props;

    if (this.isLoading()) {
      return <Loader loading={true} />;
    }

    return (
      <>
        <CitizenInfo citizen={this.state} />

        <Formik
          enableReinitialize={true}
          initialValues={this.getInitialValues()}
          onSubmit={this.handleSubmit}
          validationSchema={wizardPersonalDataSchema}
          validateOnBlur={true}
          validateOnChange={false}
        >
          {props => {
            const { values, setFieldValue } = props;

            return (
              <Form id={styles.personal_info} onKeyDown={handlePreventEnterKey}>
                <div className={styles.personal_info_form}>
                  <div className="ui form">
                    <div
                      className={classnames(
                        'fields',
                        styles.field_group,
                        styles.dynamic_fields
                      )}
                    >
                      <div className={classnames(styles.column, 'field')}>
                        <label htmlFor="phoneNumber">
                          <span>
                            {t('loan.steps.personal_info.phone_number')}
                          </span>
                        </label>
                        <Input
                          name="phoneNumber"
                          type="tel"
                          disabled
                          value={this.state.phoneNumber}
                        />
                      </div>
                      <div className={classnames(styles.column, 'field')}>
                        <label htmlFor="email">
                          <span>{t('loan.steps.personal_info.email')}</span>
                        </label>
                        <Validation name="email" showMessage={false}>
                          <Input
                            type="email"
                            name="email"
                            value={values.email}
                            placeholder={t(
                              'loan.steps.personal_info.email_example'
                            )}
                          />
                        </Validation>
                      </div>
                    </div>
                    <div
                      className={classnames(
                        'fields',
                        styles.column,
                        styles.field_group,
                        styles.column_no_margin
                      )}
                    >
                      <div className={classnames('field', styles.field)}>
                        <label htmlFor="additionalPhoneNumber">
                          <span>
                            {t(
                              'loan.steps.personal_info.additional_phone_number'
                            )}
                          </span>
                        </label>
                        <Validation
                          name="additionalPhoneNumber"
                          showMessage={false}
                        >
                          <div
                            name="additionalPhoneNumber"
                            className={classnames('phone_input_container')}
                          >
                            <PhoneNumberInput
                              value={values.additionalPhoneNumber}
                              handleChange={e => {
                                this.handleChange(e, setFieldValue);
                              }}
                              mask={PHONE_NUMBER_MASK}
                              placeholder={'99 XXX XXX'}
                            />
                          </div>
                        </Validation>
                      </div>
                      <div className={classnames('field', styles.field)}>
                        <label htmlFor="firstName">
                          <span>
                            {t('loan.steps.personal_info.first_name')}
                          </span>
                        </label>
                        <Input
                          name="firstName"
                          type="text"
                          disabled
                          value={this.state.firstName}
                        />
                      </div>
                      <div className={classnames('field', styles.field)}>
                        <label htmlFor="lastName">
                          <span>{t('loan.steps.personal_info.last_name')}</span>
                        </label>
                        <Input
                          name="lastName"
                          type="text"
                          disabled
                          value={this.state.lastName}
                        />
                      </div>
                      <div className={classnames('field', styles.field)}>
                        <label htmlFor="birthdate">
                          <span>{t('loan.steps.personal_info.birthdate')}</span>
                        </label>
                        <Input
                          name="birthdate"
                          type="text"
                          disabled
                          value={moment(this.state.birthDate).format(
                            BIRTH_DATE_FORMAT
                          )}
                        />
                      </div>
                      <div className={classnames('field', styles.field)}>
                        <label htmlFor="social_card">
                          <span>
                            {t('loan.steps.personal_info.social_card')}
                          </span>
                        </label>
                        <Input
                          name="social_card"
                          type="text"
                          disabled
                          value={this.state.socCardNumber}
                        />
                      </div>
                    </div>

                    <div
                      className={classnames(
                        'fields',
                        styles.column,
                        styles.field_group,
                        styles.column_no_margin
                      )}
                    >
                      <div className={classnames('field', styles.field)}>
                        <label htmlFor="country">
                          <span>{t('loan.steps.personal_info.country')}</span>
                        </label>
                        <Input
                          name="country"
                          type="text"
                          disabled
                          value={this.state.citizenship}
                        />
                      </div>
                      <div className={classnames('field', styles.field)}>
                        <label htmlFor="city">
                          <span>{t('loan.steps.personal_info.city')}</span>
                        </label>
                        <Input
                          name="city"
                          type="text"
                          disabled
                          value={this.state.city}
                        />
                      </div>
                      <div className={classnames('field', styles.field)}>
                        <label htmlFor="address">
                          <span>{t('loan.steps.personal_info.address')}</span>
                        </label>
                        <Input
                          name="address"
                          type="text"
                          disabled
                          value={this.composeAddress()}
                        />
                      </div>
                      <div className={classnames('field', styles.field)}>
                        <label htmlFor="notificationMethod">
                          <span>
                            {t('loan.steps.personal_info.notification.method')}
                          </span>
                        </label>
                        <Validation
                          name="notificationMethod"
                          showMessage={false}
                        >
                          <Dropdown
                            fluid
                            selection
                            name="notificationMethod"
                            className={styles.notification_dropdown}
                            onChange={(event, data) => {
                              setFieldValue('notificationMethod', data.value);
                            }}
                            value={values.notificationMethod}
                            options={NOTIFICATION_METHODS}
                          />
                        </Validation>
                      </div>
                      <div className={classnames('field', styles.field)}>
                        <label htmlFor="disputeSolution">
                          <span>
                            {t(
                              'loan.steps.personal_info.dispute_solution.method'
                            )}
                          </span>
                        </label>
                        <Validation name="disputeSolution" showMessage={false}>
                          <Dropdown
                            fluid
                            selection
                            name="disputeSolution"
                            className={styles.notification_dropdown}
                            onChange={(event, data) => {
                              setFieldValue(
                                'disputeSolutionMethod',
                                data.value
                              );
                            }}
                            value={values.disputeSolutionMethod}
                            options={DISPUTE_SOLUTION}
                          />
                        </Validation>
                      </div>
                    </div>

                    <Validation name="isChecked" showMessage={false}>
                      <Checkbox
                        className={styles.personal_info_confirmation_text}
                        label={t('loan.steps.personal_info.confirmation_text')}
                        checked={values.isChecked}
                      />
                    </Validation>

                    <Navigation wizardBag={wizardBag} />
                  </div>
                </div>
                <ErrorFocus serverError={loan.error} />
              </Form>
            );
          }}
        </Formik>
      </>
    );
  }
}

PersonalInfoStep.propTypes = {
  wizardBag: PropTypes.object,
};

function mapStateToProps(state) {
  const { loan } = state;

  return { loan };
}

const mapDispatchToProps = dispatch => {
  return {
    getPersonalInfo: () => dispatch(getPersonalInfo()),
    updatePersonalInfo: (data, cb) => dispatch(updatePersonalInfo(data, cb)),
  };
};

export default compose(
  withNamespaces('translations'),
  connect(mapStateToProps, mapDispatchToProps)
)(PersonalInfoStep);
