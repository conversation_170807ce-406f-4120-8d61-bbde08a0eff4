@import '../../../../styles/variables';
@import '../../../../styles/sizes';
@import '../../../../styles/mixins';

#personal_info {
  background-color: $white;
  border-top-left-radius: 35px;
  border-top-right-radius: 35px;
  padding: 0 20px;
  max-width: 650px;
  margin: 0 auto;

  .personal_info_form {
    padding-top: 10px;

    .field_group {
      span {
        vertical-align: super;
      }
    }

    .column {
      display: block;
      width: 50%;
      float: left;

      .field {
        margin-bottom: 40px;
      }

      input {
        width: 100%;
        padding: 0 0 1px 16px;
      }
    }

    .notification_dropdown {
      i {
        padding: 8px;
      }
    }

    .personal_info_confirmation_text {
      margin-top: 10px;
      margin-bottom: 30px;
      padding: 0 12px;

      label {
        padding-left: 25px;
        font-size: 9px;
        line-height: initial;
        font-style: oblique;
        color: $black;
        font-weight: normal;
      }
    }
  }
}

@media screen and (max-width: $lowers-tablet-width) {
  #personal_info {
    padding: 0 15px;

    .personal_info_form {
      .column {
        display: block;
        width: 100%;
        padding: 0;
        margin: 0 0 1em;

        .field {
          margin: 0 0 1em;
          padding: 0;
        }

        &.column_no_margin {
          margin-bottom: 0;
        }
      }

      .dynamic_fields {
        display: block;
      }
    }
  }
}
