import React, { Component } from 'react';
import { withNamespaces, Trans } from 'react-i18next';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { withRouter } from 'react-router-dom';
import { But<PERSON>, Checkbox } from 'semantic-ui-react';
import PropTypes from 'prop-types';
import querystring from 'query-string';
import classnames from 'classnames';
import { Formik, Form } from 'formik';
import _ from 'lodash';
import { Timer } from 'ao-components';

import {
  smsValidationCodeSchema,
  smsGetCodeSchema,
} from '../../../../validation/schemas/smsCodeSchema';
import Validation from '../../../Validation';
import SmsValidationInput from '../../../SmsValidationInput';
import Navigation from '../../../CreditWizard/Navigation';
import ErrorFocus from '../../../Validation/ErrorFocus';
import Loader from '../../../Loader';
import {
  DOCUMENT_POLLING_INTERVAL,
  DOCUMENT_POLLING_TIMEOUT,
  LOAN_TYPES,
  TIMER_EXP,
  HIDDEN_PHONE_NUMBER,
} from '../../../../constants';
import { getSuuid } from '../../../../helpers/auth';
import { getLoanType } from '../../../../helpers';
import { PHONE_NUMBER, PHONE_NUMBER_2 } from '../../../../constants';
import { handlePreventEnterKey } from '../../../../helpers/input';
import SvgComponent from '../../../SvgComponent';
import { API_URL_OTCL } from '../../../../config';

import {
  getCode,
  validateCode,
  expireCode,
} from '../../../../store/ducks/smsValidation';
import { fetchLoan, getLoanDocuments } from '../../../../store/ducks/loan';

import styles from './index.module.scss';

class SmsValidationStep extends Component {
  constructor(props) {
    super(props);

    this.state = {
      startTimer: false,
      timerStopped: false,
      showExpirationText: false,
      documentGenerationFailure: false,
      termsAccepted: false,
      loanPdfsConfirmed: false,
      agreement: false,
      documentPollingHandle: null,
    };

    this.startPollingOnce = _.once(this.startPolling);

    this.loanType = getLoanType();
  }

  componentDidUpdate(prevProps) {
    const {
      smsValidation,
      loan: { data, documents },
      wizardError,
    } = this.props;

    // Check if there is loan data and it doesn't have publicId
    // It means that loan isn't completed so we can start polling document
    if (
      Object.keys(data).length &&
      !this.state.documentPollingHandle &&
      !smsValidation.publicId
    ) {
      this.startPollingOnce();
    }

    if (
      !smsValidation.error &&
      prevProps.smsValidation !== smsValidation &&
      !smsValidation.loading &&
      smsValidation.publicId
    ) {
      const queryString = querystring.stringify({
        public_id: smsValidation.publicId,
      });
      window.location.replace(
        `${API_URL_OTCL}/app/loan/confirmation?${queryString}`
      );
    }

    if (
      prevProps.smsValidation !== smsValidation &&
      !smsValidation.loading &&
      smsValidation.data &&
      smsValidation.data.sent &&
      !smsValidation.error
    ) {
      this.setState({
        startTimer: true,
        timerStopped: false,
        showExpirationText: false,
      });
    }

    const type = this.getType();
    const loanDocumentsCount = this.getDocumentsCount(documents);

    if (
      (documents &&
        documents !== prevProps.loan.documents &&
        loanDocumentsCount === type.documents_count) ||
      wizardError
    ) {
      this.stopPolling();
    }
  }

  componentWillUnmount() {
    clearInterval(this.state.documentPollingHandle);

    if (getSuuid()) {
      this.props.expireCode();
    }
  }

  componentDidMount() {
    window.scroll(0, 0);

    this.props.fetchLoan();
    this.props.getLoanDocuments();
  }

  startPolling = () => {
    const documentPollingHandle = setInterval(
      this.props.getLoanDocuments,
      DOCUMENT_POLLING_INTERVAL
    );

    // Setting timeout to abort polling after some time
    setTimeout(() => {
      this.stopPolling();
      this.setState({
        documentGenerationFailure: true,
      });
    }, DOCUMENT_POLLING_TIMEOUT);

    this.setState({
      documentPollingHandle,
    });
  };

  stopPolling() {
    clearInterval(this.state.documentPollingHandle);
    this.setState({
      documentPollingHandle: null,
    });
  }

  handleSubmit = payload => {
    const { validateCode } = this.props;

    validateCode({
      ...payload,
    });
  };

  onTimerEnd = () => {
    this.setState({
      timerStopped: true,
      showExpirationText: true,
    });
  };

  requestCode = () => {
    this.setState(
      {
        startTimer: false,
        timerStopped: false,
        showExpirationText: false,
      },
      this.props.getCode
    );
  };

  isDisabled = () => {
    const { smsValidation } = this.props;

    return smsValidation.data && smsValidation.data.sent === false;
  };

  formatPhoneNumber(number) {
    return `+374 
    ${number.substr(4, 2)}
    ${number.substr(6, 2)} 
    ${number.substr(8, 2)} 
    ${number.substr(-2)}
    `;
  }

  getType = () => {
    if (this.loanType === LOAN_TYPES.OVL.id) {
      return LOAN_TYPES.OVL;
    }
  };

  composePdfConfirmationLabel = loanDocuments => {
    const type = this.getType();
    const loanDocumentsCount = this.getDocumentsCount(loanDocuments);

    if (loanDocumentsCount === type.documents_count) {
      const groupedDocs = _.groupBy(loanDocuments, d => d.documentType);

      const sortedDocs = _.reduce(
        type.documents,
        (result, d) => {
          if (groupedDocs[d]) {
            result.push(groupedDocs[d].shift());
          }

          return result;
        },
        []
      );

      const components = sortedDocs.map(doc => {
        return (
          <a
            target="_blank"
            rel="noopener noreferrer"
            href={doc && doc.fullPath}
          >
            text
          </a>
        );
      });

      const confirmationText = `${type.name}_pdf_confirmation_text`;

      return (
        <label>
          <Trans
            defaults={`loan.steps.sms_validation.${confirmationText}`}
            components={components}
          />
        </label>
      );
    }
  };

  getDocumentsCount = loanDocuments => {
    const type = this.getType();
    const loanDocumentsTypes = _.map(loanDocuments, 'documentType');

    return _.reduce(
      type.documents,
      (count, n) => {
        if (_.includes(loanDocumentsTypes, n)) {
          ++count;
        }

        return count;
      },
      0
    );
  };

  handleCheckboxChange = (
    target,
    formikSetFieldValue,
    subFormikSetFieldValue
  ) => {
    this.setState({
      [target.id]: target.checked,
    });
    formikSetFieldValue(target.id, target.checked);
    subFormikSetFieldValue(target.id, target.checked);
  };

  isLoading = () => {
    const {
      loan: { loading, documents },
      smsValidation,
    } = this.props;

    const { documentPollingHandle, documentGenerationFailure } = this.state;

    const type = this.getType();
    const loanDocumentsCount = this.getDocumentsCount(documents);

    const loadingDocuments = loanDocumentsCount !== type.documents_count;

    return (
      loading ||
      !!documentPollingHandle ||
      smsValidation.loading ||
      (!loading && loadingDocuments && !documentGenerationFailure)
    );
  };

  render() {
    const {
      t,
      wizardBag,
      loan: {
        data: { citizen },
        documents,
      },
      smsValidation,
    } = this.props;

    const {
      startTimer,
      timerStopped,
      showExpirationText,
      documentGenerationFailure,
    } = this.state;

    const phoneNumber =
      citizen && citizen.phoneNumber
        ? this.formatPhoneNumber(citizen.phoneNumber)
        : HIDDEN_PHONE_NUMBER;

    return (
      <Formik
        initialValues={{
          termsAccepted: false,
          agreement: false,
          code: null,
          loanPdfsConfirmed: false,
        }}
        onSubmit={this.handleSubmit}
        validationSchema={smsValidationCodeSchema}
        validateOnBlur={true}
        validateOnChange={false}
      >
        {formik => {
          return (
            <Form
              id={styles.sms_validation_step}
              onKeyDown={handlePreventEnterKey}
            >
              <div className={styles.sms_validation_step_form}>
                <div className="ui form">
                  <Loader loading={this.isLoading()} />

                  <SvgComponent
                    className={styles.sms_phone_icon}
                    name="sms-phone-icon"
                  />
                  <div className={styles.phone_number_container}>
                    <div className={styles.text}>
                      <Trans
                        defaults={'loan.steps.sms_validation.phone_number'}
                        components={[<strong>{{ phoneNumber }}</strong>]}
                      />
                    </div>
                  </div>

                  <div className={styles.middle_text}>
                    {t('loan.steps.sms_validation.middle_text')}
                  </div>

                  <div
                    className={classnames([
                      styles.code_container,
                      styles.code_input_container,
                    ])}
                  >
                    <Validation name="code" showMessage={false}>
                      <SmsValidationInput
                        onChange={value => {
                          formik.setFieldValue('code', value);
                        }}
                      />
                    </Validation>
                  </div>

                  {!timerStopped &&
                    smsValidation.data &&
                    smsValidation.data.sent && (
                      <Timer
                        className={styles.timer}
                        content={t('loan.steps.sms_validation.timer_text')}
                        countdownStart={TIMER_EXP}
                        startTimer={startTimer}
                        onTimerEnd={this.onTimerEnd}
                      />
                    )}
                  {showExpirationText && (
                    <div className={styles.expiration_text}>
                      {t('loan.steps.sms_validation.timer_expired_text')}
                    </div>
                  )}
                  {this.isDisabled() && (
                    <div className={styles.expiration_text}>
                      {t('loan.sms_identification_attempts_text')}
                    </div>
                  )}
                  <Formik
                    initialValues={{
                      termsAccepted: false,
                      loanPdfsConfirmed: false,
                      agreement: false,
                    }}
                    onSubmit={this.requestCode}
                    validationSchema={smsGetCodeSchema}
                    validateOnBlur={true}
                    validateOnChange={false}
                  >
                    {subFormik => {
                      if (formik.isValidating) {
                        formik.isValidating = false;
                        subFormik.setFieldTouched('termsAccepted');
                        subFormik.setFieldTouched('loanPdfsConfirmed');
                        subFormik.setFieldTouched('agreement');
                      }
                      return (
                        <div className={styles.sms_validation_step_sub_form}>
                          {!documents && documentGenerationFailure && (
                            <div className={styles.documents_error_container}>
                              <p className={styles.documents_error_text}>
                                {t(
                                  'loan.steps.confirmation.documents_error_text',
                                  [PHONE_NUMBER, PHONE_NUMBER_2]
                                )}
                              </p>
                            </div>
                          )}
                          <Validation
                            name="loanPdfsConfirmed"
                            showMessage={false}
                          >
                            <Checkbox
                              onChange={(event, target) => {
                                this.handleCheckboxChange(
                                  target,
                                  formik.setFieldValue,
                                  subFormik.setFieldValue
                                );
                              }}
                              className={
                                styles.sms_validation_step_pdf_confirmation_text
                              }
                              label={this.composePdfConfirmationLabel(
                                documents
                              )}
                              checked={this.state.loanPdfsConfirmed}
                            />
                          </Validation>
                          <Validation name="termsAccepted" showMessage={false}>
                            <Checkbox
                              onChange={(event, target) => {
                                this.handleCheckboxChange(
                                  target,
                                  formik.setFieldValue,
                                  subFormik.setFieldValue
                                );
                              }}
                              className={
                                styles.sms_validation_step_confirmation_text
                              }
                              label={t(
                                'loan.steps.sms_validation.confirmation_text'
                              )}
                              checked={this.state.termsAccepted}
                            />
                          </Validation>
                          <Validation name="agreement" showMessage={false}>
                            <Checkbox
                              name="agreement"
                              onChange={(event, target) => {
                                this.handleCheckboxChange(
                                  target,
                                  formik.setFieldValue,
                                  subFormik.setFieldValue
                                );
                              }}
                              className={
                                styles.sms_validation_step_agreement_text
                              }
                              label={t(
                                'loan.steps.sms_validation.agreement_text'
                              )}
                              checked={this.state.agreement}
                            />
                          </Validation>
                          <div className={styles.get_code_container}>
                            {(smsValidation.data && smsValidation.data.sent) ||
                            this.isDisabled() ? (
                              <Button
                                type="button"
                                className={styles.resend_button}
                                disabled={this.isDisabled()}
                                onClick={this.requestCode}
                              >
                                {t(
                                  'loan.steps.sms_validation.resend_button_text'
                                )}
                              </Button>
                            ) : (
                              <Button
                                type="button"
                                className={styles.button}
                                disabled={this.isDisabled()}
                                onClick={subFormik.handleSubmit}
                              >
                                {t('loan.steps.sms_validation.button_text')}
                              </Button>
                            )}
                          </div>
                          <ErrorFocus />
                        </div>
                      );
                    }}
                  </Formik>
                </div>
              </div>
              <Navigation wizardBag={wizardBag} />
            </Form>
          );
        }}
      </Formik>
    );
  }
}

SmsValidationStep.propTypes = {
  wizardBag: PropTypes.object,
};

function mapStateToProps(state) {
  const { loan, smsValidation } = state;

  return { loan, smsValidation };
}

const mapDispatchToProps = dispatch => {
  return {
    fetchLoan: () => dispatch(fetchLoan()),
    validateCode: data => dispatch(validateCode(data)),
    expireCode: () => dispatch(expireCode()),
    getCode: () => dispatch(getCode()),
    getLoanDocuments: () => dispatch(getLoanDocuments()),
  };
};

export default compose(
  withRouter,
  withNamespaces('translations'),
  connect(mapStateToProps, mapDispatchToProps)
)(SmsValidationStep);
