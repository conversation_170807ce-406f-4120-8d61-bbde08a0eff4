@import '../../../../styles/variables';
@import '../../../../styles/sizes';

#sms_validation_step {
  padding: 20px 15px 0 15px;
  background: $white;

  .sms_validation_step_form {
    margin: 0 auto 40px auto;
    max-width: 600px;
    text-align: center;

    .sms_validation_step_sub_form {
      position: relative;
      margin-top: 75px;
    }

    .sms_validation_step_pdf_confirmation_text {
      margin-bottom: 10px;
      margin-top: 3px;
      text-align: left;
      word-break: break-word;
    }

    .sms_validation_step_confirmation_text,
    .sms_validation_step_agreement_text {
      margin-top: 10px;
      text-align: left;
    }

    .sms_validation_step_pdf_confirmation_text,
    .sms_validation_step_confirmation_text,
    .sms_validation_step_agreement_text {
      label {
        padding-left: 25px;
        font-size: 9px;
        line-height: initial;
        font-style: oblique;
        color: $dark-gray;
        font-weight: normal;
      }
    }

    .sms_validation_step_confirmation_text {
      margin-bottom: 10px;
    }

    .phone_number_container {
      text-align: center;
      max-width: 300px;
      margin: 0 auto;

      .text {
        color: $black;
      }
    }

    .middle_text {
      font-size: 14px;
      text-align: center;
      color: $black;
      margin-top: 20px;
    }

    .code_container {
      margin-top: 20px;
      display: inline-block;

      .text {
        color: $main-text-color;
        font-size: 14px;
        top: 2px;
        right: 16px;
        position: relative;
      }
    }

    .get_code_container {
      position: absolute;
      top: -70px;
      left: 0;
      right: 0;
      margin-left: auto;
      margin-right: auto;

      button {
        vertical-align: middle;
        border: none;
        background: white;
        color: $light-black;
        text-decoration: underline;
        font-weight: normal;
      }
    }

    .expiration_text {
      color: $pink;
      display: inline-block;
      width: 100%;
      font-size: 14px;
      margin-top: 8px;
    }

    .timer {
      margin-top: 12px;
      color: $dark-gray;
      font-size: 12px;
    }

    @media screen and (max-width: $mobile-width) {
      .code_container {
        width: 300px;
        display: inline-block;
      }

      .timer {
        font-size: 10px;
      }
    }
  }
}
