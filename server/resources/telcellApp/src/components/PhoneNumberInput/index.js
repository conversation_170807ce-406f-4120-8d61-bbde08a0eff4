import React, { Component } from 'react';
import InputMask from 'react-input-mask';
import { Input } from 'semantic-ui-react';
import { PHONE_NUMBER_MASK } from '../../constants';

import styles from './index.module.scss';

class PhoneNumberInput extends Component {
  render() {
    const { value, handleChange } = this.props;

    return (
      <div id={styles.phone_number_input_component}>
        <InputMask
          id="phone"
          key={value}
          type={'tel'}
          autoFocus={!!value.length}
          mask={PHONE_NUMBER_MASK}
          maskChar={null}
          value={value}
          autoComplete="off"
          onChange={handleChange}
        >
          {props => (
            <Input
              {...props}
              label={{
                basic: true,
                content: (
                  <div className={styles.phone_number_input_label}>
                    <div className={styles.phone_number_input_label_code}>
                      +374
                    </div>
                  </div>
                ),
              }}
              labelPosition="left"
              placeholder="(00) 111 111"
              className={styles.phone_number_input}
            />
          )}
        </InputMask>
      </div>
    );
  }
}

export default PhoneNumberInput;
