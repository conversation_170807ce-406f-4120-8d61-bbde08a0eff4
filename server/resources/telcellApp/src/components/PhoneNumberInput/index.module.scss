@import '../../styles/variables';
@import '../../styles/sizes';

#phone_number_input_component {
  .phone_number_input {
    width: 100%;
    height: 34px;
    display: flex;

    input {
      width: unset !important;
      font-size: 16px;
      height: 34px;
      float: right;
      border-bottom: 1px solid $gray;
      padding: 0 !important;
    }

    input:focus {
      outline: none;
    }

    :global(.ui.basic.label) {
      border: none;
      border-bottom: 1px solid $gray;
      border-radius: 0;
      line-height: 12px;
    }

    .phone_number_input_label {
      .phone_number_input_label_code {
        font-size: 16px;
        font-weight: 100;
      }
    }
  }

  @media screen and (max-width: $mobile-width) {
    :global(.ui.basic.label) {
      line-height: 10px !important;
    }
  }
}
