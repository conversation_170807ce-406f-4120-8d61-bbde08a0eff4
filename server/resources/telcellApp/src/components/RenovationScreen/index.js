import React, { Component } from 'react';
import { withNamespaces } from 'react-i18next';
import { compose } from 'redux';

import styles from './index.module.scss';

class RenovationScreen extends Component {
  render() {
    const { t } = this.props;

    return (
      <div id={styles.renovation_screen}>
        <div>
          <div className={styles.renovation_screen_title}>
            {t('renovation_screen.title')}
          </div>
        </div>
      </div>
    );
  }
}

export default compose(withNamespaces('translations'))(RenovationScreen);
