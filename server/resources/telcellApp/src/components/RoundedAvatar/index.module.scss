@import '../../styles/variables';
@import '../../styles/mixins';

.avatar_container {
  .avatar,
  .avatar_xs {
    background-size: cover;
    margin: 0 auto;
    background-repeat: no-repeat;
  }

  .avatar {
    @include circle;
  }

  .avatar_xs {
    @include circle_xs;
  }

  .no_avatar,
  .no_avatar_xs {
    @include circle;
    background: $lighter-gray;
    color: $white;
    margin: 0 auto;
    text-align: center;
  }

  .no_avatar {
    @include circle;
    font-size: 57px;
    line-height: 100px;
  }

  .no_avatar_xs {
    @include circle_xs;
    font-size: 37px;
    line-height: 55px;
  }
}
