import React, { Component } from 'react';
import PropTypes from 'prop-types';

import styles from './index.module.scss';

class SmsValidationInput extends Component {
  constructor(props) {
    super(props);

    this.state = {
      length: 4,
    };

    this.inputs = [];

    this.focusedCells = 0;
  }

  componentDidUpdate() {
    this.inputs.forEach((input, index) => {
      if (input === null) {
        this.inputs.splice(index, 1);
      }
      return null;
    });
  }

  onClick = () => {
    // Focus empty
    let input = this.inputs.find((input, index) => {
      return !input.value;
    });

    input = input || this.inputs[this.inputs.length - 1];
    input.focus();
  };

  onChange(i) {
    // Focus next
    const reg = new RegExp(/^[0-9]*$/);
    const concat = (accumulator, currentValue) =>
      accumulator + currentValue.value;
    const str = this.inputs.reduce(concat, '');

    if (!reg.test(str)) {
      return (this.inputs[i].value = null);
    }

    if (this.inputs[i].value.length > 1) {
      this.inputs[i].value = this.inputs[i].value[0];
    }

    if (i < this.inputs.length - 1) {
      this.inputs[i + 1].type = 'tel';
      this.inputs[i + 1].focus();
    }

    const value = this.extractValue();
    this.props.onChange(value);
  }

  onFocus = () => {
    this.focusedCells++;
  };

  onBlur = e => {
    this.focusedCells--;
    if (typeof this.props.onBlur === 'function') {
      // See https://reactjs.org/docs/events.html#event-pooling
      e.persist();

      setTimeout(() => {
        if (!this.focusedCells) {
          this.props.onBlur(e);
        }
      }, 0);
    }
  };

  onKeydown(i, e) {
    const TAB = 9;
    const SPACE = 32;
    // Euler number = 2.71...
    const EULER = 69;
    const BACKSPACE = 8;
    const code = e.which || e.keyCode;

    if (
      code === TAB ||
      code === SPACE ||
      ((+this.inputs[0].value || i > 1) && code === EULER)
    ) {
      e.preventDefault();
    }

    if (code === BACKSPACE) {
      e.preventDefault();

      if (i === 0) {
        return;
      }

      // Clear last input
      if (i === this.inputs.length - 1 && this.inputs[i].value) {
        this.inputs[i].value = '';
        this.inputs[i].focus();
      } else {
        // Clear and focus previous input
        const input = this.inputs[i - 1];
        input.value = '';
        input.focus();
      }

      const value = this.extractValue();
      this.props.onChange(value);
    }
  }

  extractValue() {
    return this.inputs.map(input => input.value).reduce((acc, v) => acc + v);
  }

  render() {
    const { name, className } = this.props;
    const range = [...Array(this.state.length).keys()];

    return (
      <div id={styles.sms_validation} className={className}>
        {range.map(index => {
          return (
            <div className={styles.input_container} key={index}>
              <input
                name={name}
                type="tel"
                ref={input => {
                  this.inputs[index] = input;
                }}
                maxLength="1"
                className={styles.input_cell}
                onClick={this.onClick}
                onChange={e => {
                  this.onChange(index);
                }}
                onKeyDown={e => {
                  this.onKeydown(index, e);
                }}
                onFocus={this.onFocus}
                onBlur={this.onBlur}
                autoComplete="off"
                placeholder="X"
              />
            </div>
          );
        })}
      </div>
    );
  }
}

SmsValidationInput.propTypes = {
  onChange: PropTypes.func,
};

export default SmsValidationInput;
