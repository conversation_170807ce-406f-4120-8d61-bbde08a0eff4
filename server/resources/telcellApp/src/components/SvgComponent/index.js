import React from 'react';
import PropTypes from 'prop-types';

import svg from '../../spriteSvg/svg';

const SvgComponent = ({ name, className, onClick }) => {
  return (
    <span
      onClick={onClick}
      dangerouslySetInnerHTML={{ __html: svg(name, className) }}
    />
  );
};

SvgComponent.propTypes = {
  name: PropTypes.string.isRequired,
  onClick: PropTypes.func,
};

export default SvgComponent;
