import React, { Component } from 'react';
import classnames from 'classnames';
import { compose } from 'redux';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';
import { withNamespaces } from 'react-i18next';

import styles from './index.module.scss';
import { fetchTermsInfo } from '../../store/ducks/termsInfo';
import querystring from 'query-string';
import Loader from '../Loader';
import i18n from '../../i18n';
import SvgComponent from '../SvgComponent';
import Currency from '../Currency';
import { getLoanType, storageCleanUp } from '../../helpers';
import { LOAN_TYPES } from '../../constants';
import { InternalServerError } from '../InternalServerError';

class TermsAndConditions extends Component {
  constructor(props) {
    super(props);

    window.recaptchaOptions = {
      lang: i18n.language,
    };
  }

  componentDidMount() {
    const { fetchTermsInfo } = this.props;

    storageCleanUp();

    fetchTermsInfo({
      loan_type_id: this._getLoanTypeId(),
    });
  }

  _getLoanTypeId = () => {
    const { location } = this.props;

    if (location.search) {
      const { loan_type_id } = querystring.parse(this.props.location.search);

      return loan_type_id;
    }

    return getLoanType() || LOAN_TYPES.OVL.id;
  };

  isLoading() {
    const {
      termsInfo: { loading },
    } = this.props;

    return loading;
  }

  render() {
    const {
      t,
      termsInfo: { data: termsInfo = {}, error },
    } = this.props;

    if (this.isLoading() || !termsInfo) {
      return <Loader loading={true} />;
    }

    if (error) {
      return (
        <div className={styles.header}>
          <SvgComponent name="cash_me" />
          <InternalServerError t={t} />
        </div>
      );
    }

    return (
      <>
        <div className={styles.container}>
          <main className={styles.terms_content}>
            <div className={styles.row}>
              <div className={classnames(styles.col_md_12, styles.header)}>
                <SvgComponent name="cash_me" />
                <h3>{t('terms_and_conditions.title')}</h3>
              </div>
            </div>
            <div className={styles.row}>
              <div className={classnames(styles.col_md_12, styles.body)}>
                <div className={styles.col_md_6}>
                  <h5 className={styles.title}>
                    {t('terms_and_conditions.borrower')}
                  </h5>
                </div>
                <div className={styles.col_md_6}>
                  <p className={styles.item}>{termsInfo.borrower}</p>
                </div>
                <div className={styles.col_md_6}>
                  <h5 className={styles.title}>
                    {t('terms_and_conditions.age')}
                  </h5>
                </div>
                <div className={styles.col_md_6}>
                  <p className={styles.item}>{termsInfo.age}</p>
                </div>
                <div className={styles.col_md_6}>
                  <h5 className={styles.title}>
                    {t('terms_and_conditions.currency')}
                  </h5>
                </div>
                <div className={styles.col_md_6}>
                  <p className={styles.item}>{termsInfo.currency}</p>
                </div>
                <div className={styles.col_md_6}>
                  <h5 className={styles.title}>
                    {t('terms_and_conditions.delivery_procedure')}
                  </h5>
                </div>
                <div className={styles.col_md_6}>
                  <p className={styles.item}>{termsInfo.provisionType}</p>
                </div>
                <div className={styles.col_md_6}>
                  <h5 className={styles.title}>
                    {t('terms_and_conditions.loan_amount')}
                  </h5>
                </div>
                <div className={styles.col_md_6}>
                  <div
                    className={classnames(styles.item, styles.loan_amount_item)}
                  >
                    <Currency hasSign={false} value={termsInfo.minAmount} />
                    <Currency hasSign={false} value={termsInfo.maxAmount} /> ՀՀ
                    դրամ
                  </div>
                </div>
                <div className={styles.col_md_6}>
                  <h5 className={styles.title}>
                    {t('terms_and_conditions.loan_term')}
                  </h5>
                </div>
                <div className={styles.col_md_6}>
                  <p className={styles.item}>{termsInfo.loanTerm} ամիս</p>
                </div>
                <div className={styles.col_md_6}>
                  <h5 className={styles.title}>
                    {t('terms_and_conditions.annual_rate')}
                  </h5>
                </div>
                <div className={styles.col_md_6}>
                  <p className={styles.item}>{termsInfo.annualRate} %</p>
                </div>
                <div className={styles.col_md_6}>
                  <h5 className={styles.title}>
                    {t('terms_and_conditions.apr')}
                  </h5>
                </div>
                <div className={styles.col_md_6}>
                  <p className={styles.item}>
                    {termsInfo.apr} % (կախված ֆայքո սքոր ցուցանիշից)
                  </p>
                </div>
                <div className={styles.col_md_6}>
                  <h5 className={styles.title}>
                    {t('terms_and_conditions.service_fee')}
                  </h5>
                </div>
                <div className={styles.col_md_6}>
                  <p className={styles.item}>
                    {termsInfo.serviceFee} % մնացորդի նկատմամբ
                  </p>
                </div>
                <div className={styles.col_md_6}>
                  <h5 className={styles.title}>
                    {t('terms_and_conditions.loan_provision_fee')}
                  </h5>
                </div>
                <div className={styles.col_md_6}>
                  <p className={styles.item}>{termsInfo.loanProvisionFee}</p>
                </div>
                <div className={styles.col_md_6}>
                  <h5 className={styles.title}>
                    {t('terms_and_conditions.loan_payment')}
                  </h5>
                </div>
                <div className={styles.col_md_6}>
                  <p className={styles.item}>{termsInfo.loanPayment}</p>
                </div>
                <div className={styles.col_md_6}>
                  <h5 className={styles.title}>
                    {t('terms_and_conditions.loan_security')}
                  </h5>
                </div>
                <div className={styles.col_md_6}>
                  <p className={styles.item}>{termsInfo.loanSecurity}</p>
                </div>
                <div className={styles.col_md_6}>
                  <h5 className={styles.title}>
                    {t('terms_and_conditions.loan_security')}
                  </h5>
                </div>
                <div className={styles.col_md_6}>
                  <p className={styles.item}>{termsInfo.loanCollateralRatio}</p>
                </div>
                <div className={styles.col_md_6}>
                  <h5 className={styles.title}>
                    {t('terms_and_conditions.application_review_fee')}
                  </h5>
                </div>
                <div className={styles.col_md_6}>
                  <p className={styles.item}>
                    {termsInfo.applicationReviewFee}
                  </p>
                </div>
                <div className={styles.col_md_6}>
                  <h5 className={styles.title}>
                    {t('terms_and_conditions.withdrawal_fee')}
                  </h5>
                </div>
                <div className={styles.col_md_6}>
                  <p className={styles.item}>{termsInfo.withdrawalFeeRate} %</p>
                </div>
                <div className={styles.col_md_6}>
                  <h5 className={styles.title}>
                    {t('terms_and_conditions.penalties_fines')}
                  </h5>
                </div>
                <div className={styles.col_md_6}>
                  <p className={styles.item}>
                    ժամկետանց գումարի տույժ՝ օրական{' '}
                    {termsInfo.penaltyBaseAmount} %, ժամկետանց տոկոսի տույժ՝
                    օրական {termsInfo.penaltyPercentageAmount} %
                  </p>
                </div>
                <div className={styles.col_md_6}>
                  <h5 className={styles.title}>
                    {t('terms_and_conditions.early_loan_repayment_penalty')}
                  </h5>
                </div>
                <div className={styles.col_md_6}>
                  <p className={styles.item}>
                    {termsInfo.earlyLoanRepaymentPenalty}
                  </p>
                </div>
                <div className={styles.col_md_6}>
                  <h5 className={styles.title}>
                    {t('terms_and_conditions.required_documents')}
                  </h5>
                </div>
                <div className={styles.col_md_6}>
                  <div className={styles.item}>
                    {termsInfo.requiredDocuments.map(index => {
                      return <li key={index}>{index}</li>;
                    })}
                  </div>
                </div>
                <div className={styles.col_md_6}>
                  <h5 className={styles.title}>
                    {t('terms_and_conditions.loan_decision')}
                  </h5>
                </div>
                <div className={styles.col_md_6}>
                  <p className={styles.item}>{termsInfo.loanDecision}</p>
                </div>
                <div className={styles.col_md_6}>
                  <h5 className={styles.title}>
                    {t('terms_and_conditions.decision_rejection_factors')}
                  </h5>
                </div>
                <div className={styles.col_md_6}>
                  <p className={styles.item}>
                    Դրական որոշման գործոններ` հաճախորդի դրական վարկային
                    պատմություն, եկամուտների բավարար մակարդակ և գրավադրվող
                    մեքենայի տեխնիկական վիճակի համապատասխանություն Վարկային
                    կազմակերպության կողմից սահմանված պահանջներին <br />
                    Մերժման գործոններ` հաճախորդի բացասական վարկային պատմություն,
                    եկամուտների ոչ բավարար մակարդակ և/կամ գրավադրվող մեքենայի
                    տեխնիկական վիճակի անհամապատասխանություն Վարկային
                    կազմակերպության կողմից սահմանված պահանջներին
                  </p>
                </div>
              </div>
            </div>
          </main>
        </div>
      </>
    );
  }
}

const mapStateToProps = state => {
  const { termsInfo } = state;

  return { termsInfo };
};

const mapDispatchToProps = dispatch => {
  return {
    fetchTermsInfo: loanTypeId => dispatch(fetchTermsInfo(loanTypeId)),
  };
};

export default compose(
  withRouter,
  withNamespaces('translations'),
  connect(mapStateToProps, mapDispatchToProps)
)(TermsAndConditions);
