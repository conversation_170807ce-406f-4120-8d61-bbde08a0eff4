@import '../../styles/variables';

.container {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
  color: $dark-gray;
  font-size: 14px;
  font-family: 'DejaVu Sans Book Arm', sans-serif !important;
  background-color: $light-gray;
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}

.col_md_6,
.col_md_12 {
  position: relative;
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  padding-top: 10px;
  float: left;
}

.terms_content .title {
  color: $darker-gray;
  font-size: 1.25rem;
}

.header {
  text-align: center;
  padding: 10px 0;
  margin: 15px 10px 20px 10px;
}

.body {
  background-color: white;
  border-top-left-radius: 40px;
  border-top-right-radius: 40px;
  padding-bottom: 50px;
}

.header {
  svg {
    height: auto;
    width: 40%;
    max-width: 220px;
    min-width: 160px;
  }

  h3 {
    margin: 3px auto 0 auto;
    color: $dark-gray;
    text-transform: uppercase;
    letter-spacing: 1px;
    max-width: 260px;
  }
}

.terms_content {
  min-height: 100%;
  padding-top: 10px;
}

.terms_content .item {
  font-size: 14px;
  padding-bottom: 10px;
  border-bottom: 1px solid $gray;
}

.terms_content .loan_amount_item {
  display: flex;

  div {
    &:last-child {
      span {
        margin-right: 4px;

        &::before {
          content: '-';
          display: inline;
          margin: 0 3px;
        }
      }
    }
  }
}

.col_md_6 {
  &:last-child {
    .item {
      border-bottom: none;
    }
  }
}

@media (min-width: 576px) {
  .container {
    max-width: 540px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 720px;
  }
}

@media (min-width: 992px) {
  .container {
    max-width: 960px;
  }
}

@media (min-width: 1200px) {
  .container {
    max-width: 1140px;
  }
}

@media (min-width: 992px) {
  .col_md_6 {
    flex: 0 0 50%;
    max-width: 50%;
  }

  .col_md_12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
}
