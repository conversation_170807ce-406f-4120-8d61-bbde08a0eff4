import React from 'react';
import { withNamespaces } from 'react-i18next';
import { compose } from 'redux';
import { connect } from 'formik';
import _ from 'lodash';
import scrollToElement from 'scroll-to-element';

class ErrorFocus extends React.Component {
  transformServerErrorsToObject = serverErrors => {
    const { t } = this.props;

    const errors = _.reduce(
      serverErrors,
      function(result, value, key) {
        result[key] = t(`validation_errors.${value[0].code}`);
        return result;
      },
      {}
    );

    return errors;
  };

  componentDidUpdate(prevProps) {
    const { errors, setErrors, isSubmitting, isValidating } = prevProps.formik;
    const { serverError } = this.props;

    const validationErrors = Object.keys(errors).length && errors;
    const serverErrors = serverError && serverError.errors;

    if (serverErrors && prevProps.serverError !== serverError) {
      setErrors(this.transformServerErrorsToObject(serverErrors));
    }

    const err = validationErrors || serverErrors;
    const key = err && Object.keys(err)[0];

    const errorElement = document.querySelector(` [name="${key}"] `);

    if (errorElement) {
      if (key && isSubmitting && !isValidating) {
        scrollToElement(errorElement, {
          duration: 10,
          align: 'middle',
        });
      }
    }
  }

  render() {
    return null;
  }
}

export default compose(withNamespaces('translations'))(connect(ErrorFocus));
