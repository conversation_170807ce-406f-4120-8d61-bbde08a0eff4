@import '../../styles/variables';
@import '../../styles/sizes';

#vehicle_number_input_component {
  height: 36px;

  .vehicle_number_input {
    width: 265px;
    height: 100%;
    display: inline-block;

    input {
      width: 210px;
      font-size: 22px;
      font-weight: normal;
      height: 100%;
      float: right;
      border: none !important;
      font-family: $main-font-family;
      padding-left: 20px;
    }

    input:focus {
      outline: none;
      border: none;
    }

    input::placeholder {
      font-family: $main-font-family;
    }

    div:first-child {
      width: 55px;
      padding: 0 8px 0 0;
      border: none;
      position: relative;
    }

    .vehicle_number_input_label {
      .flag_container {
        display: flex;

        .vehicle_flag_label {
          width: 30px;
          font-size: 13px;
          margin-left: 20px;
          font-weight: bold;
          margin-top: 3px;

          .text {
            margin-top: 5px;
          }

          .flag {
            .flag_red {
              width: 20px;
              height: 5.21px;
              background: $red;
              border-radius: 0;
            }

            .flag_blue {
              width: 20px;
              height: 5.21px;
              background: $blue;
            }

            .flag_orange {
              width: 20px;
              height: 5.21px;
              background: $web-orange;
            }
          }
        }
      }

      .vehicle_number_input_label_flag {
        margin: 5px 0 0 0;
      }

      .vehicle_number_input_label_text {
        margin-top: 3px;
      }

      .vehicle_number_input_label_seperator {
        margin-top: 3px;
        height: 30px;
        margin-left: 8px;
        border-left: 2px solid $light-gray;
      }
    }
  }
}
