import React, { Component } from 'react';
import InputMask from 'react-input-mask';
import { Input } from 'semantic-ui-react';
import { withNamespaces } from 'react-i18next';
import { compose } from 'redux';

import { getLetterInputType } from '../../helpers/input';
import { TECH_PASSPORT_MASK } from '../../constants';

import techPassport from '../../images/tech-passport.svg';

import styles from './index.module.scss';

class VehicleTechPassportInput extends Component {
  getInputType = length => {
    if (length < 2) {
      return getLetterInputType();
    }

    return 'tel';
  };

  render() {
    const { values, handleChange } = this.props;

    return (
      <div id={styles.vehicle_input_component}>
        <InputMask
          id={'passport'}
          key={values.techPassport}
          autoFocus={!!values.techPassport.length}
          type={this.getInputType(values.techPassport.length)}
          mask={TECH_PASSPORT_MASK}
          maskChar={null}
          value={values.techPassport}
          autoComplete="off"
          onChange={handleChange}
        >
          {props => (
            <div>
              <Input
                {...props}
                label={{
                  basic: true,
                  content: (
                    <div className={styles.vehicle_number_input_label}>
                      <div className={styles.label_container}>
                        <div>
                          <img src={techPassport} alt="tech_passport" />
                        </div>
                      </div>
                      <div className={styles.vehicle_input_label_seperator} />
                    </div>
                  ),
                }}
                labelPosition="left"
                placeholder="XX 1111111"
                className={styles.vehicle_passport_input}
              />
            </div>
          )}
        </InputMask>
      </div>
    );
  }
}

export default compose(withNamespaces('translations'))(
  VehicleTechPassportInput
);
