@import '../../styles/variables';
@import '../../styles/sizes';

#vehicle_input_component {
  .vehicle_passport_input {
    width: 265px;
    height: 36px;
    display: inline-block;

    input {
      width: 220px;
      font-size: 22px;
      font-weight: normal;
      height: 36px;
      border: none !important;
      font-family: $main-font-family;
      padding-left: 30px;
    }

    input:focus {
      outline: none;
      border: none;
    }

    input::placeholder {
      font-family: $main-font-family;
    }

    div:first-child {
      width: 45px;
      height: 36px;
      padding: 0 8px 0 0;
      border: none;
      position: relative;
      margin-top: 2px;
    }

    .vehicle_number_input_label {
      display: inline-flex;
      margin-left: 12px;
      height: 36px;

      .vehicle_number_input_label_flag {
        margin: 5px 0 0 0;
      }

      .vehicle_number_input_label_text {
        margin-top: 3px;
      }

      .vehicle_input_label_seperator {
        height: 30px;
        margin-left: 1px;
        border-left: 2px solid $light-gray;
      }
    }
  }

  .input_text {
    display: block;
    margin-left: 10px;
    font-size: 12px;
    font-style: italic;
  }

  @media screen and (max-width: $mobile-width) {
    .vehicle_passport_input {
      div:first-child {
        margin-top: 1px;
      }
    }
  }
}
