import axios from 'axios';
import humps from 'humps';
import { cloneDeep } from 'lodash';

import { API_URL, MULTIPART_FORM_DATA } from './index';

const instance = axios.create();

axios.defaults.baseURL = API_URL;

instance.interceptors.request.use(
  config => {
    // We need skip decamelize keys in this step if content-type is multipart/form-data
    if (config.headers['Content-Type'] !== MULTIPART_FORM_DATA) {
      config = {
        ...config,
        data: humps.decamelizeKeys(config.data),
      };
    }

    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

instance.interceptors.response.use(
  response => {
    if (response.status >= 200 && response.status <= 400) {
      return {
        ...response,
        data: humps.camelizeKeys(response.data),
      };
    }

    return response;
  },
  error => {
    if (error.response && error.response.status == 422) {
      const responseCopy = cloneDeep(error.response);
      responseCopy.data.error.errors = humps.camelizeKeys(
        error.response.data.error.errors
      );

      return Promise.reject({ response: responseCopy });
    }

    return Promise.reject(error);
  }
);

export default instance;
