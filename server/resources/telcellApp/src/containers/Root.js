import React, { Component } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';

import App from './App';
import { fetchAppStatus } from '../store/ducks/appConfigs';
import Loader from '../components/Loader';
import RenovationScreen from '../components/RenovationScreen';

class Root extends Component {
  componentDidMount() {
    this.props.appStatus();
  }

  render() {
    const {
      appConfigs: { appStatusLoading, data },
    } = this.props;

    if (appStatusLoading) {
      return <Loader loading={appStatusLoading} />;
    }

    if (data.disabledAPP) {
      return <RenovationScreen />;
    }

    return <App {...this.props} />;
  }
}

const mapStateToProps = state => {
  const { appConfigs } = state;

  return { appConfigs };
};

const mapDispatchToProps = dispatch => {
  return {
    appStatus: data => dispatch(fetchAppStatus(data)),
  };
};

export default compose(connect(mapStateToProps, mapDispatchToProps))(Root);
