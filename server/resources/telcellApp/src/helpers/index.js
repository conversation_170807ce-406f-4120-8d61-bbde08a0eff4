import { LATEST_STEP, LOAN_TYPE, LOAN_TYPES } from '../constants';
import { removeSuuid } from './auth';

export const isDev = () => {
  return process.env.NODE_ENV === 'development';
};

export const isProd = () => {
  return process.env.MIX_REACT_APP_CUSTOM_ENV === 'production';
};

export const setLoanType = loanType => {
  sessionStorage.setItem(LOAN_TYPE, loanType);
};

export const getLoanType = () => {
  return +sessionStorage.getItem(LOAN_TYPE);
};

export const removeLoanType = () => {
  sessionStorage.removeItem(LOAN_TYPE);
};

export const setLatestStep = stepId => {
  sessionStorage.setItem(LATEST_STEP, stepId);
};

export const getLatestStep = () => {
  return sessionStorage.getItem(LATEST_STEP);
};

export const removeLatestStep = () => {
  sessionStorage.removeItem(LATEST_STEP);
};

export const openDocument = url => {
  const link = document.createElement('a');

  link.href = url;
  link.target = '_blank';
  link.click();
};

export const storageCleanUp = () => {
  removeLatestStep();
  removeLoanType();
  removeSuuid();
};
