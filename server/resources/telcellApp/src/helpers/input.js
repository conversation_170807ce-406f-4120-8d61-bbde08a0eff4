import { isIOS, isMobileSafari, isSafari } from 'react-device-detect';

import { ENTER_KEY } from '../constants';

export const getLetterInputType = () => {
  // We have input selection issue on IOS devices if type='text', that's why we need this check
  if (isIOS || isSafari || isMobileSafari) {
    return 'email';
  }

  return 'text';
};

export const handlePreventEnterKey = keyEvent => {
  if (keyEvent.key === ENTER_KEY.NAME || keyEvent.keyCode === ENTER_KEY.CODE) {
    keyEvent.target.blur();
    keyEvent.preventDefault();
  }
};
