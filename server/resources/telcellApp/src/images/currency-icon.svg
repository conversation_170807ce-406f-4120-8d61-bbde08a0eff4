<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="135" height="135" viewBox="0 0 135 135">
  <defs>
    <filter id="Ellipse_61" x="0" y="0" width="135" height="135" filterUnits="userSpaceOnUse">
      <feOffset dy="4" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="8" result="blur"/>
      <feFlood flood-color="#19191d" flood-opacity="0.078"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <linearGradient id="linear-gradient" x1="0.128" y1="0.103" x2="0.769" y2="0.91" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff4f4"/>
      <stop offset="1" stop-color="#fff"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="0.188" y1="0.117" x2="0.77" y2="0.913" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff"/>
      <stop offset="0.493" stop-color="#e7343f"/>
      <stop offset="1" stop-color="#d73a49"/>
    </linearGradient>
    <filter id="Subtraction_9" x="0" y="0" width="135" height="135" filterUnits="userSpaceOnUse">
      <feOffset dy="4" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="8" result="blur-2"/>
      <feFlood flood-color="#19191d" flood-opacity="0.078"/>
      <feComposite operator="in" in2="blur-2"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g id="Group_974" data-name="Group 974" transform="translate(-140 -186)">
    <g transform="matrix(1, 0, 0, 1, 140, 186)" filter="url(#Ellipse_61)">
      <circle id="Ellipse_61-2" data-name="Ellipse 61" cx="43.5" cy="43.5" r="43.5" transform="translate(24 20)" fill="#fff"/>
    </g>
    <circle id="Ellipse_61-3" data-name="Ellipse 61" cx="43.5" cy="43.5" r="43.5" transform="translate(164 206)" fill="url(#linear-gradient)"/>
    <g transform="matrix(1, 0, 0, 1, 140, 186)" filter="url(#Subtraction_9)">
      <path id="Subtraction_9-2" data-name="Subtraction 9" d="M-6516.5,5666a43.224,43.224,0,0,1-16.932-3.419,43.321,43.321,0,0,1-13.826-9.323,43.361,43.361,0,0,1-9.323-13.827A43.254,43.254,0,0,1-6560,5622.5a43.248,43.248,0,0,1,3.418-16.932,43.353,43.353,0,0,1,9.323-13.826,43.348,43.348,0,0,1,13.826-9.322A43.23,43.23,0,0,1-6516.5,5579a43.243,43.243,0,0,1,16.934,3.418,43.348,43.348,0,0,1,13.826,9.322,43.358,43.358,0,0,1,9.323,13.826A43.224,43.224,0,0,1-6473,5622.5a43.233,43.233,0,0,1-3.418,16.933,43.371,43.371,0,0,1-9.323,13.827,43.353,43.353,0,0,1-13.826,9.323A43.237,43.237,0,0,1-6516.5,5666Zm0-85.62a41.864,41.864,0,0,0-16.394,3.31,41.972,41.972,0,0,0-13.388,9.027,41.979,41.979,0,0,0-9.027,13.388,41.874,41.874,0,0,0-3.31,16.394,41.879,41.879,0,0,0,3.31,16.4,41.981,41.981,0,0,0,9.027,13.388,41.975,41.975,0,0,0,13.388,9.026,41.864,41.864,0,0,0,16.394,3.31,41.873,41.873,0,0,0,16.4-3.31,41.987,41.987,0,0,0,13.389-9.026,41.988,41.988,0,0,0,9.024-13.388,41.862,41.862,0,0,0,3.311-16.4,41.858,41.858,0,0,0-3.311-16.394,41.992,41.992,0,0,0-9.024-13.388,42,42,0,0,0-13.389-9.027A41.873,41.873,0,0,0-6516.5,5580.38Z" transform="translate(6584 -5559)" fill="url(#linear-gradient-2)"/>
    </g>
    <path id="Union_3" data-name="Union 3" d="M609.741-918.09A6.749,6.749,0,0,1,603-924.832a6.749,6.749,0,0,1,6.741-6.741h.76c-.093-.185-.183-.371-.269-.557a20.229,20.229,0,0,1-1.393-4.1,20.372,20.372,0,0,1-.488-4.438A20.354,20.354,0,0,1,628.682-961a20.355,20.355,0,0,1,20.332,20.332,20.428,20.428,0,0,1-.488,4.438,20.233,20.233,0,0,1-1.393,4.1c-.087.186-.176.373-.269.557h.759a6.749,6.749,0,0,1,6.741,6.741,6.749,6.749,0,0,1-6.741,6.742Zm-3.531-6.742a3.535,3.535,0,0,0,3.531,3.532h37.881a3.536,3.536,0,0,0,3.532-3.532,3.536,3.536,0,0,0-3.532-3.531h-2.766a20.441,20.441,0,0,1-1.361,1.6h3.056a1.605,1.605,0,0,1,1.6,1.606,1.6,1.6,0,0,1-1.6,1.6H611.025a1.6,1.6,0,0,1-1.6-1.6,1.605,1.605,0,0,1,1.6-1.606h2.842a20.43,20.43,0,0,1-1.36-1.6h-2.767A3.535,3.535,0,0,0,606.21-924.832Zm5.565-15.836a16.821,16.821,0,0,0,2.707,9.168,17.01,17.01,0,0,0,3.073,3.55,17.054,17.054,0,0,0,1.527,1.192h19.2a17.064,17.064,0,0,0,1.527-1.192,17.037,17.037,0,0,0,3.073-3.55,16.822,16.822,0,0,0,2.707-9.168,16.926,16.926,0,0,0-16.907-16.907A16.926,16.926,0,0,0,611.775-940.668Zm18.572,7.967v-4.846h-3.121v-2.026h3.121v-1.421h-3.121v-2h3.121v-.653q0-3.237-3.238-3.237a2.939,2.939,0,0,0-2.446.967,5.06,5.06,0,0,0-.769,3.133H620.61a7.175,7.175,0,0,1,1.631-4.938,6.259,6.259,0,0,1,4.938-1.841,6.445,6.445,0,0,1,4.8,1.678,6.09,6.09,0,0,1,1.653,4.472V-943h3.122v2h-3.122v1.421h3.122v2.026h-3.122v4.846Z" transform="translate(-421.182 1187.795)" fill="#c83d34"/>
  </g>
</svg>
