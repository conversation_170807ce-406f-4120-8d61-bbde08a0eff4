<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="135" height="135" viewBox="0 0 135 135">
  <defs>
    <filter id="Ellipse_61" x="0" y="0" width="135" height="135" filterUnits="userSpaceOnUse">
      <feOffset dy="4" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="8" result="blur"/>
      <feFlood flood-color="#19191d" flood-opacity="0.078"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <linearGradient id="linear-gradient" x1="0.128" y1="0.103" x2="0.769" y2="0.91" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff4f4"/>
      <stop offset="1" stop-color="#fff"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="0.188" y1="0.117" x2="0.77" y2="0.913" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff"/>
      <stop offset="0.493" stop-color="#e7343f"/>
      <stop offset="1" stop-color="#d73a49"/>
    </linearGradient>
    <filter id="Subtraction_1" x="0" y="0" width="135" height="135" filterUnits="userSpaceOnUse">
      <feOffset dy="4" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="8" result="blur-2"/>
      <feFlood flood-color="#19191d" flood-opacity="0.078"/>
      <feComposite operator="in" in2="blur-2"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g id="Group_970" data-name="Group 970" transform="translate(-140 -127)">
    <g transform="matrix(1, 0, 0, 1, 140, 127)" filter="url(#Ellipse_61)">
      <circle id="Ellipse_61-2" data-name="Ellipse 61" cx="43.5" cy="43.5" r="43.5" transform="translate(24 20)" fill="#fff"/>
    </g>
    <circle id="Ellipse_61-3" data-name="Ellipse 61" cx="43.5" cy="43.5" r="43.5" transform="translate(164 147)" fill="url(#linear-gradient)"/>
    <g transform="matrix(1, 0, 0, 1, 140, 127)" filter="saturate(0.5)">
      <path id="Subtraction_1-2" data-name="Subtraction 1" d="M-6516.5,5666a43.224,43.224,0,0,1-16.932-3.419,43.321,43.321,0,0,1-13.826-9.323,43.361,43.361,0,0,1-9.323-13.827A43.254,43.254,0,0,1-6560,5622.5a43.248,43.248,0,0,1,3.418-16.932,43.353,43.353,0,0,1,9.323-13.826,43.348,43.348,0,0,1,13.826-9.322A43.23,43.23,0,0,1-6516.5,5579a43.243,43.243,0,0,1,16.934,3.418,43.348,43.348,0,0,1,13.826,9.322,43.358,43.358,0,0,1,9.323,13.826A43.224,43.224,0,0,1-6473,5622.5a43.233,43.233,0,0,1-3.418,16.933,43.371,43.371,0,0,1-9.323,13.827,43.353,43.353,0,0,1-13.826,9.323A43.237,43.237,0,0,1-6516.5,5666Zm0-85.62a41.864,41.864,0,0,0-16.394,3.31,41.972,41.972,0,0,0-13.388,9.027,41.979,41.979,0,0,0-9.027,13.388,41.874,41.874,0,0,0-3.31,16.394,41.879,41.879,0,0,0,3.31,16.4,41.981,41.981,0,0,0,9.027,13.388,41.975,41.975,0,0,0,13.388,9.026,41.864,41.864,0,0,0,16.394,3.31,41.873,41.873,0,0,0,16.4-3.31,41.987,41.987,0,0,0,13.389-9.026,41.988,41.988,0,0,0,9.024-13.388,41.862,41.862,0,0,0,3.311-16.4,41.858,41.858,0,0,0-3.311-16.394,41.992,41.992,0,0,0-9.024-13.388,42,42,0,0,0-13.389-9.027A41.873,41.873,0,0,0-6516.5,5580.38Z" transform="translate(6584 -5559)" fill="url(#linear-gradient-2)"/>
    </g>
    <g id="Group_950" data-name="Group 950" transform="translate(-9.584 30)">
      <path id="Path_1459" data-name="Path 1459" d="M150.256,76.1H130.677a.948.948,0,0,0-.194-.08,5.632,5.632,0,0,1-4.813-5.8q-.006-21.249,0-42.5a5.733,5.733,0,0,1,5.888-5.885q9.021-.014,18.04.01a6.39,6.39,0,0,1,1.868.286,5.591,5.591,0,0,1,3.9,5.615q0,8.133-.009,16.267-.009,13.115-.016,26.234a5.648,5.648,0,0,1-3.969,5.578C151.008,75.93,150.63,76.006,150.256,76.1ZM128.672,29.9V67.588h24.046V29.9ZM140.428,74.41a2.534,2.534,0,0,0-.007-5.068,2.534,2.534,0,1,0,.007,5.068Zm.172-49.188c-.85,0-1.691-.009-2.537,0-.6.008-.956.325-.964.825-.012.524.353.861.977.865q2.509.013,5.021,0a.863.863,0,0,0,.971-.824c.014-.51-.369-.858-.984-.866C142.257,25.211,141.428,25.222,140.6,25.222Zm-4.428.85a.839.839,0,1,0-1.617.352.841.841,0,0,0,.462.464.839.839,0,0,0,.655-.009.849.849,0,0,0,.273-.193.838.838,0,0,0,.227-.614Z" transform="translate(75.585 111.528)" fill="#8f3021"/>
      <path id="Path_1460" data-name="Path 1460" d="M145.474,40.136a2.789,2.789,0,0,0-2.789,2.789V60.768a.7.7,0,0,0,1.191.492l3.979-3.979a1.4,1.4,0,0,1,.986-.409h13.37A2.789,2.789,0,0,0,165,54.083V42.925a2.789,2.789,0,0,0-2.789-2.789Z" transform="translate(75.585 111.528)" fill="#8f3021"/>
      <circle id="Ellipse_3" data-name="Ellipse 3" cx="2.291" cy="2.291" r="2.291" transform="translate(220.889 158.225)" fill="#fff"/>
      <circle id="Ellipse_4" data-name="Ellipse 4" cx="2.291" cy="2.291" r="2.291" transform="translate(227.434 158.225)" fill="#fff"/>
      <circle id="Ellipse_5" data-name="Ellipse 5" cx="2.291" cy="2.291" r="2.291" transform="translate(233.979 158.225)" fill="#fff"/>
    </g>
  </g>
</svg>
