import React from 'react';
import ReactD<PERSON> from 'react-dom';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as Router } from 'react-router-dom';
import { I18nextProvider } from 'react-i18next';
import { Provider } from 'react-redux';

import RootComponent from './containers/Root';
import i18n from './i18n';
import { configureStore } from './store/configureStore';

import './index.scss';

import { WALLET_ROUTE_PREFIXES } from './config';

const store = configureStore();

if (document.getElementById('root')) {
  const apiParams = document.getElementById('root').getAttribute('api-params');

  ReactDOM.render(
    <Provider store={store}>
      <I18nextProvider i18n={i18n}>
        <Router basename={WALLET_ROUTE_PREFIXES.OTCL}>
          <RootComponent apiParams={apiParams && JSON.parse(apiParams)} />
        </Router>
      </I18nextProvider>
    </Provider>,
    document.getElementById('root')
  );
}
