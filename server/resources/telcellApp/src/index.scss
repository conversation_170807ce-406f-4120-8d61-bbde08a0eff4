@import '~semantic-ui-css/semantic.min.css';
@import 'styles/variables';
@import 'styles/fonts';

html {
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  font-family: $main-font-family;
  background-color: $light-gray !important;
}

// Global overrides
:global(.ui.header),
.ui.button,
.ui.form .field input,
.ui.menu {
  font-family: $main-font-family;
}

:global(.ui.form .fields) {
  margin: 0 0 1em;
}

:global(.ui.active.dimmer) {
  position: fixed;
  z-index: 999999;
  background-color: rgba(0, 0, 0, 0.4);
}

:global(.ui.modal > .content) {
  padding: 0 !important;
}

:global(.ui) {
  .selection.dropdown {
    border: 1px solid $darky-gray;
    min-height: 34px;
    padding: 8px 16px;
    font-size: 16px;

    .text {
      width: 90%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .menu {
      .text {
        width: auto;
        white-space: normal;
        overflow-x: hidden;
        text-overflow: unset;
      }
    }

    .icon {
      float: right;
      color: $secondary;
      font-size: 14px;
    }
  }
}

:global(.ui.form .field) {
  input {
    border: none;
    border-bottom: 1px solid $gray;
    height: 34px;
    color: $black;
    width: 250px;
    font-size: 16px;
    border-radius: 0 !important;

    &:disabled {
      background-color: $smoke-gray;
    }

    &:focus {
      border-color: $gray;
    }
  }
}

:global(.ui.disabled.input),
.ui.input:not(.disabled),
input[disabled],
.ui.form .disabled.field,
.ui.form .disabled.fields .field,
.ui.form .field :disabled {
  opacity: 1;
}

:global(.ui.disabled.input > input) {
  text-overflow: ellipsis;
}

:global(.ui.form .fields > .field) {
  margin: 12px 13px;

  label {
    color: $main-text-color;
    font-size: 12px;
    font-weight: initial;
    margin-bottom: 0;
  }
}

:global(.ui.checkbox input[type='checkbox']) {
  & ~ label::before {
    border-radius: 50%;
  }

  & ~ label::after {
    padding-top: 3px;
    padding-right: 1px;
    font-size: 10px;
    color: $white !important;
    background-color: $main;
    border-radius: 50%;
  }
}
