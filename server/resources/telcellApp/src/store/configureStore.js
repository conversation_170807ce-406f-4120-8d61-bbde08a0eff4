import { applyMiddleware, createStore, compose } from 'redux';
import { createEpicMiddleware } from 'redux-observable';

import { isDev } from '../helpers';
import { rootEpic, rootReducer } from './ducks';

const epicMiddleware = createEpicMiddleware();

export const configureStore = () => {
  const middleWare = [epicMiddleware];

  // In development, use the browser's Redux dev tools extension if installed
  const enhancers = [];
  if (
    isDev() &&
    typeof window !== 'undefined' &&
    window.__REDUX_DEVTOOLS_EXTENSION__
  ) {
    enhancers.push(window.window.__REDUX_DEVTOOLS_EXTENSION__());
  }

  const initialState = {};
  const store = createStore(
    rootReducer,
    initialState,
    compose(applyMiddleware(...middleWare), ...enhancers)
  );

  epicMiddleware.run(rootEpic);

  return store;
};
