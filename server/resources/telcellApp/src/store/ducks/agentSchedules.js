import { ofType } from 'redux-observable';
import { mergeMap } from 'rxjs/operators';
import { getSuuidHeader } from '../../helpers/auth';
import axios from '../../config/axios';

export const FETCH_AGENT_SCHEDULES =
  'globalcredit/agent_schedules/FETCH_AGENT_SCHEDULES';
export const AGENT_SCHEDULES_FETCHED =
  'globalcredit/agent_schedules/AGENT_SCHEDULES_FETCHED';
export const ERROR_FETCHING_AGENT_SCHEDULES =
  'globalcredit/agent_schedules/ERROR_FETCHING_AGENT_SCHEDULES';

export const fetchAgentSchedules = () => ({
  type: FETCH_AGENT_SCHEDULES,
});

export const fetchAgentSchedulesFulfilled = payload => ({
  type: AGENT_SCHEDULES_FETCHED,
  payload,
});

export const fetchAgentSchedulesError = payload => ({
  type: ERROR_FETCHING_AGENT_SCHEDULES,
  payload,
});

const initialState = {
  data: null,
  loading: false,
};

export default function reducer(state = initialState, action) {
  switch (action.type) {
    case FETCH_AGENT_SCHEDULES:
      return {
        loading: true,
      };
    case AGENT_SCHEDULES_FETCHED:
      return {
        data: action.payload.data,
        loading: false,
      };
    case ERROR_FETCHING_AGENT_SCHEDULES:
      return {
        error: action.payload,
        loading: false,
      };
    default:
      return state;
  }
}

export const agentSchedulesEpic = action$ =>
  action$.pipe(
    ofType(FETCH_AGENT_SCHEDULES),
    mergeMap(() =>
      axios
        .get('fetch-agent-schedules', {
          headers: getSuuidHeader(),
        })
        .then(response => {
          return fetchAgentSchedulesFulfilled(response.data);
        })
        .catch(error => {
          return fetchAgentSchedulesError(
            error.response && error.response.data.error
          );
        })
    )
  );
