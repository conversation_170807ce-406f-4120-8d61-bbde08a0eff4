import { ofType } from 'redux-observable';
import { mergeMap } from 'rxjs/operators';
import { getSuuidHeader } from '../../helpers/auth';
import axios from '../../config/axios';

export const CASH_OFFICES_FETCH =
  'globalcredit/cash_offices/CASH_OFFICES_FETCH';
export const CASH_OFFICES_FETCHED =
  'globalcredit/cash_offices/CASH_OFFICES_FETCHED';
export const CASH_OFFICES_ERROR =
  'globalcredit/cash_offices/CASH_OFFICES_ERROR';

export const CASH_OFFICES_BY_PUBLIC_ID_FETCH =
  'globalcredit/cash_offices/CASH_OFFICES_BY_PUBLIC_ID_FETCH';
export const CASH_OFFICES_BY_PUBLIC_ID_FETCHED =
  'globalcredit/cash_offices/CASH_OFFICES_BY_PUBLIC_ID_FETCHED';
export const CASH_OFFICES_BY_PUBLIC_ID_ERROR =
  'globalcredit/cash_offices/CASH_OFFICES_BY_PUBLIC_ID_ERROR';

export const fetchCashOffices = document_number => ({
  type: CASH_OFFICES_FETCH,
  document_number,
});

export const fetchCashOfficesFulfilled = payload => ({
  type: CASH_OFFICES_FETCHED,
  payload,
});

export const fetchCashOfficesError = payload => ({
  type: CASH_OFFICES_ERROR,
  payload,
});

export const fetchCashOfficesByPublicId = payload => ({
  type: CASH_OFFICES_BY_PUBLIC_ID_FETCH,
  payload,
});

export const fetchCashOfficesByPublicIdFulfilled = payload => ({
  type: CASH_OFFICES_BY_PUBLIC_ID_FETCHED,
  payload,
});

export const fetchCashOfficesByPublicIdError = payload => ({
  type: CASH_OFFICES_BY_PUBLIC_ID_ERROR,
  payload,
});

const initialState = {
  data: null,
  loading: false,
};

export default function reducer(state = initialState, action) {
  switch (action.type) {
    case CASH_OFFICES_FETCH:
      return {
        loading: true,
      };
    case CASH_OFFICES_FETCHED:
      return {
        data: action.payload.data,
        loading: false,
      };
    case CASH_OFFICES_ERROR:
      return {
        error: action.payload,
        loading: false,
      };
    case CASH_OFFICES_BY_PUBLIC_ID_FETCH:
      return {
        loading: true,
      };
    case CASH_OFFICES_BY_PUBLIC_ID_FETCHED:
      return {
        data: action.payload.data,
        loading: false,
      };
    case CASH_OFFICES_BY_PUBLIC_ID_ERROR:
      return {
        error: action.payload,
        loading: false,
      };
    default:
      return state;
  }
}

export const cashOfficesEpic = action$ =>
  action$.pipe(
    ofType(CASH_OFFICES_FETCH),
    mergeMap(() =>
      axios
        .get('cash-offices', {
          headers: getSuuidHeader(),
        })
        .then(response => {
          return fetchCashOfficesFulfilled(response.data);
        })
        .catch(error => {
          return fetchCashOfficesError(
            error.response && error.response.data.error
          );
        })
    )
  );

export const fetchCashOfficesByPublicIdEpic = action$ =>
  action$.pipe(
    ofType(CASH_OFFICES_BY_PUBLIC_ID_FETCH),
    mergeMap(action =>
      axios
        .get(`fetch-cash-offices?public_id=${action.payload}`)
        .then(response => {
          return fetchCashOfficesByPublicIdFulfilled(response.data);
        })
        .catch(error => {
          return fetchCashOfficesByPublicIdError(
            error.response && error.response.data.error
          );
        })
    )
  );
