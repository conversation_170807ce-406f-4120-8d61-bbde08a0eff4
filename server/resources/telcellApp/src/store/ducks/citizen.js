import { ofType } from 'redux-observable';
import { mergeMap, catchError } from 'rxjs/operators';
import { of, from } from 'rxjs';
import _ from 'lodash';

import { API_URL_OTCL } from '../../config';
import { getSuuid } from '../../helpers/auth';
import { wizardError, wizardErrorClear } from './wizard';
import axios from '../../config/axios';

export const CITIZEN_FETCH = 'globalcredit/citizen/CITIZEN_FETCH';
export const CITIZEN_FETCHED = 'globalcredit/citizen/CITIZEN_FETCHED';
export const CITIZEN_ERROR = 'globalcredit/citizen/CITIZEN_ERROR';

export const TRADE_VEHICLE_FETCH = 'globalcredit/citizen/TRADE_VEHICLE_FETCH';
export const TRADE_VEHICLE_FETCHED =
  'globalcredit/citizen/TRADE_VEHICLE_FETCHED';
export const TRADE_VEHICLE_ERROR = 'globalcredit/citizen/TRADE_VEHICLE_ERROR';

export const FETCH_DISCOUNT = 'globalcredit/citizen/FETCH_DISCOUNT';
export const FETCH_DISCOUNT_SUCCESS =
  'globalcredit/citizen/FETCH_DISCOUNT_SUCCESS';
export const FETCH_DISCOUNT_ERROR = 'globalcredit/citizen/FETCH_DISCOUNT_ERROR';

export const COMPOSE_CITIZEN_INFO = 'globalcredit/citizen/COMPOSE_CITIZEN_INFO';
export const COMPOSE_CITIZEN_INFO_SUCCESS =
  'globalcredit/citizen/COMPOSE_CITIZEN_INFO_SUCCESS';
export const COMPOSE_CITIZEN_INFO_ERROR =
  'globalcredit/citizen/COMPOSE_CITIZEN_INFO_ERROR';

export const STORE_REQUESTED_DETAILS =
  'globalcredit/citizen/STORE_REQUESTED_DETAILS';
export const STORE_REQUESTED_DETAILS_SUCCESS =
  'globalcredit/citizen/STORE_REQUESTED_DETAILS_SUCCESS';
export const STORE_REQUESTED_DETAILS_ERROR =
  'globalcredit/citizen/STORE_REQUESTED_DETAILS_ERROR';

export const fetchCitizen = payload => ({
  type: CITIZEN_FETCH,
  payload,
});

export const fetchCitizenFulfilled = payload => ({
  type: CITIZEN_FETCHED,
  payload,
});

export const fetchCitizenError = payload => ({
  type: CITIZEN_ERROR,
  payload,
});

export const fetchTradeVehicle = payload => ({
  type: TRADE_VEHICLE_FETCH,
  payload,
});

export const fetchTradeVehicleFulfilled = payload => ({
  type: TRADE_VEHICLE_FETCHED,
  payload,
});

export const fetchTradeVehicleError = payload => ({
  type: TRADE_VEHICLE_ERROR,
  payload,
});

export const fetchDiscount = payload => ({
  type: FETCH_DISCOUNT,
  payload,
});

export const fetchDiscountFulfilled = payload => ({
  type: FETCH_DISCOUNT_SUCCESS,
  payload,
});

export const fetchDiscountError = payload => ({
  type: FETCH_DISCOUNT_ERROR,
  payload,
});

export const composeCitizenInfo = () => ({
  type: COMPOSE_CITIZEN_INFO,
});

export const composeCitizenInfoFulfilled = payload => ({
  type: COMPOSE_CITIZEN_INFO_SUCCESS,
  payload,
});

export const composeCitizenInfoError = payload => ({
  type: COMPOSE_CITIZEN_INFO_ERROR,
  payload,
});

export const storeRequestedDetails = payload => ({
  type: STORE_REQUESTED_DETAILS,
  payload,
});

export const storeRequestedDetailsFulfilled = payload => ({
  type: STORE_REQUESTED_DETAILS_SUCCESS,
  payload,
});

export const storeRequestedDetailsError = payload => ({
  type: STORE_REQUESTED_DETAILS_ERROR,
  payload,
});

const initialState = {
  data: undefined,
  loading: false,
  discount: null,
  storeAmountSuccess: false,
};

const modifyCredit = citizen => {
  if (citizen.credit) {
    if (citizen.credit.ovl && citizen.credit.ovl.length === 0) {
      citizen.credit.ovl = null;
    }

    if (citizen.credit.ocl && citizen.credit.ocl.length === 0) {
      citizen.credit.ocl = null;
    }
  }

  return citizen;
};

const concatTradeCredit = (credit, action) => {
  if (credit && credit.length > 0) {
    return credit
      .filter(v => {
        return !v.rejected && v.vehicleInfo.tradeAmount === null;
      })
      .concat([action.payload.data]);
  }

  return [action.payload.data];
};

export default function reducer(state = initialState, action) {
  switch (action.type) {
    case CITIZEN_FETCH:
      return {
        loading: true,
      };
    case CITIZEN_FETCHED:
      const citizen = modifyCredit(action.payload.data);

      return {
        data: {
          ...citizen,
          passports: _.keyBy(citizen.passports, 'type'),
        },
        loading: false,
      };
    case CITIZEN_ERROR:
      return {
        error: action.payload,
        loading: false,
      };

    case TRADE_VEHICLE_FETCH:
      return {
        ...state,
        tradeError: null,
        tradeLoading: true,
      };
    case TRADE_VEHICLE_FETCHED:
      const tradeCredit = concatTradeCredit(state.data.credit, action);

      return {
        ...state,
        data: {
          ...state.data,
          credit: [...tradeCredit],
        },
        tradeLoading: false,
      };
    case TRADE_VEHICLE_ERROR:
      return {
        ...state,
        tradeError: action.payload,
        tradeLoading: false,
      };
    case FETCH_DISCOUNT:
      return {
        ...state,
        loading: true,
      };
    case FETCH_DISCOUNT_SUCCESS:
      return {
        ...state,
        discount: +action.payload.data.discount,
        loading: false,
      };
    case FETCH_DISCOUNT_ERROR:
      return {
        ...state,
        error: action.payload,
        loading: false,
      };
    case COMPOSE_CITIZEN_INFO:
      return {
        ...state,
        storeAmountSuccess: false,
        loading: true,
      };
    case COMPOSE_CITIZEN_INFO_SUCCESS:
      return {
        ...state,
        data: {
          ...state.data,
          ...action.payload,
        },
        loading: false,
      };
    case COMPOSE_CITIZEN_INFO_ERROR:
      return {
        ...state,
        error: action.payload,
        loading: false,
      };
    case STORE_REQUESTED_DETAILS:
      return {
        ...state,
        loading: true,
      };
    case STORE_REQUESTED_DETAILS_SUCCESS:
      return {
        ...state,
        storeAmountSuccess: action.payload.success,
        loading: false,
      };
    case STORE_REQUESTED_DETAILS_ERROR:
      return {
        ...state,
        error: action.payload,
        loading: false,
      };
    default:
      return state;
  }
}

export const citizenEpic = action$ =>
  action$.pipe(
    ofType(CITIZEN_FETCH),
    mergeMap(action =>
      from(
        axios.get('citizens', {
          params: action.payload,
          headers: {
            suuid: getSuuid(),
          },
        })
      ).pipe(
        mergeMap(response => {
          return of(fetchCitizenFulfilled(response.data), wizardErrorClear());
        }),
        catchError(error =>
          of(
            wizardError(error.response.data.error),
            fetchCitizenError(error.response && error.response.data.error)
          )
        )
      )
    )
  );

export const tradeVehicleEpic = action$ =>
  action$.pipe(
    ofType(TRADE_VEHICLE_FETCH),
    mergeMap(action =>
      from(
        axios.get('trade-vehicle', {
          params: {
            vehicle_number: action.payload.vehicleNumber,
            tech_passport: action.payload.techPassport,
            trade_amount: action.payload.tradeAmount,
          },
          headers: {
            suuid: getSuuid(),
          },
        })
      ).pipe(
        mergeMap(response => {
          return of(
            fetchTradeVehicleFulfilled(response.data),
            wizardErrorClear()
          );
        }),
        catchError(error =>
          of(
            fetchTradeVehicleError(error.response && error.response.data.error)
          )
        )
      )
    )
  );

export const fetchDiscountEpic = action$ =>
  action$.pipe(
    ofType(FETCH_DISCOUNT),
    mergeMap(action => {
      return axios
        .get('qr-get-discount', {
          params: action.payload,
        })
        .then(({ data }) => fetchDiscountFulfilled(data))
        .catch(error =>
          fetchDiscountError(error.response && error.response.data.error)
        );
    })
  );

export const composeCitizenInfoEpic = action$ =>
  action$.pipe(
    ofType(COMPOSE_CITIZEN_INFO),
    mergeMap(() => {
      return axios
        .get(`${API_URL_OTCL}/app/compose-citizen-info`, {
          headers: {
            suuid: getSuuid(),
          },
        })
        .then(({ data }) => composeCitizenInfoFulfilled(data))
        .catch(error =>
          composeCitizenInfoError(error.response && error.response.data.error)
        );
    })
  );

export const storeRequestedDetailsEpic = action$ =>
  action$.pipe(
    ofType(STORE_REQUESTED_DETAILS),
    mergeMap(action => {
      return axios
        .post(`${API_URL_OTCL}/app/store-requested-details`, action.payload, {
          headers: {
            suuid: getSuuid(),
          },
        })
        .then(({ data }) => storeRequestedDetailsFulfilled(data))
        .catch(error =>
          storeRequestedDetailsError(
            error.response && error.response.data.error
          )
        );
    })
  );
