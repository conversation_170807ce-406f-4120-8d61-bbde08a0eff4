/**
 * We use 'Ducks' proposal for combining reducers,
 * actions, action creators and epics in one file
 *
 * For more information:
 * https://github.com/erikras/ducks-modular-redux
 */

import { combineEpics } from 'redux-observable';
import { combineReducers } from 'redux';
import citizen, {
  citizenEpic,
  tradeVehicleEpic,
  fetchDiscountEpic,
  composeCitizenInfoEpic,
  storeRequestedDetailsEpic,
} from './citizen';
import agentSchedules, { agentSchedulesEpic } from './agentSchedules';
import loanConfigs, { loanConfigsForCitizenEpic } from './loanConfigs';
import appConfigs, {
  fetchAppStatusEpic,
  fetchServerTimeEpic,
} from './appConfigs';
import cashOffices, {
  cashOfficesEpic,
  fetchCashOfficesByPublicIdEpic,
} from './cashOffices';
import carVerification, {
  storeCarVerificationEpic,
  fetchCarVerificationEpic,
} from './carVerification';
import wizard from './wizard';
import transferTypes, { transferTypesEpic } from './transferTypes';
import loan, {
  fetchLoanEpic,
  approveLoanEpic,
  approvedLoanEpic,
  getPersonalInfoEpic,
  getLoanDocumentsEpic,
  storeTransferInfoEpic,
  updatePersonalInfoEpic,
  fetchLoanByPublicIdEpic,
  getLoanTermsCalculationEpic,
  getLoanScheduleEpic,
} from './loan';
import smsValidation, {
  validateCodeEpic,
  getCodeEpic,
  expireCodeEpic,
} from './smsValidation';
import termsInfo, { termInfoEpic } from './termsInfo';

export const rootEpic = combineEpics(
  citizenEpic,
  tradeVehicleEpic,
  approveLoanEpic,
  cashOfficesEpic,
  approvedLoanEpic,
  transferTypesEpic,
  agentSchedulesEpic,
  storeCarVerificationEpic,
  fetchCarVerificationEpic,
  getPersonalInfoEpic,
  getLoanDocumentsEpic,
  storeTransferInfoEpic,
  getLoanTermsCalculationEpic,
  getLoanScheduleEpic,
  fetchDiscountEpic,
  composeCitizenInfoEpic,
  storeRequestedDetailsEpic,
  updatePersonalInfoEpic,
  loanConfigsForCitizenEpic,
  fetchAppStatusEpic,
  fetchServerTimeEpic,
  fetchCashOfficesByPublicIdEpic,
  validateCodeEpic,
  fetchLoanEpic,
  getCodeEpic,
  expireCodeEpic,
  fetchLoanByPublicIdEpic,
  termInfoEpic
);

export const rootReducer = combineReducers({
  carVerification,
  agentSchedules,
  smsValidation,
  transferTypes,
  cashOffices,
  loanConfigs,
  appConfigs,
  citizen,
  wizard,
  loan,
  termsInfo,
});
