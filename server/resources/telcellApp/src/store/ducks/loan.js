import { ofType } from 'redux-observable';
import { mergeMap, catchError } from 'rxjs/operators';
import { empty, of, from } from 'rxjs';
import { getSuuidHeader } from '../../helpers/auth';
import axios from '../../config/axios';
import _ from 'lodash';
import { wizardError, wizardErrorClear } from './wizard';

export const LOAN_APPROVE = 'globalcredit/loan/LOAN_APPROVE';
export const LOAN_APPROVED = 'globalcredit/loan/LOAN_APPROVED';
export const LOAN_APPROVE_ERROR = 'globalcredit/loan/LOAN_APPROVE_ERROR';

export const LOAN_FETCH = 'globalcredit/loan/LOAN_FETCH';
export const LOAN_FETCHED = 'globalcredit/loan/LOAN_FETCHED';
export const LOAN_FETCH_ERROR = 'globalcredit/loan/LOAN_FETCH_ERROR';

export const LOAN_BY_PUBLIC_ID_FETCH =
  'globalcredit/loan/LOAN_BY_PUBLIC_ID_FETCH';
export const LOAN_BY_PUBLIC_ID_FETCHED =
  'globalcredit/loan/LOAN_BY_PUBLIC_ID_FETCHED';
export const LOAN_BY_PUBLIC_ID_FETCH_ERROR =
  'globalcredit/loan/LOAN_BY_PUBLIC_ID_FETCH_ERROR';

export const STORE_TRANSFER_INFO = 'globalcredit/loan/STORE_TRANSFER_INFO';
export const TRANSFER_INFO_STORED = 'globalcredit/loan/TRANSFER_INFO_STORED';
export const STORE_TRANSFER_INFO_ERROR =
  'globalcredit/loan/STORE_TRANSFER_INFO_ERROR';

export const GET_LOAN_DOCUMENTS = 'globalcredit/loan/GET_LOAN_DOCUMENTS';
export const GET_LOAN_DOCUMENTS_SUCCESS =
  'globalcredit/loan/GET_LOAN_DOCUMENTS_SUCCESS';
export const GET_LOAN_DOCUMENTS_ERROR =
  'globalcredit/loan/GET_LOAN_DOCUMENTS_ERROR';

export const PERSONAL_INFO_GET = 'globalcredit/loan/PERSONAL_INFO_GET';
export const PERSONAL_INFO_GET_SUCCESS =
  'globalcredit/loan/PERSONAL_INFO_GET_SUCCESS';
export const PERSONAL_INFO_GET_ERROR =
  'globalcredit/loan/PERSONAL_INFO_GET_ERROR';

export const PERSONAL_INFO_UPDATE = 'globalcredit/loan/PERSONAL_INFO_UPDATE';
export const PERSONAL_INFO_UPDATED = 'globalcredit/loan/PERSONAL_INFO_UPDATED';
export const PERSONAL_INFO_UPDATE_ERROR =
  'globalcredit/loan/PERSONAL_INFO_UPDATE_ERROR';

export const GET_LOAN_TERMS_CALCULATION =
  'globalcredit/loan/GET_LOAN_TERMS_CALCULATION';
export const GET_LOAN_TERMS_CALCULATION_SUCCESS =
  'globalcredit/loan/GET_LOAN_TERMS_CALCULATION_SUCCESS';
export const GET_LOAN_TERMS_CALCULATION_ERROR =
  'globalcredit/loan/GET_LOAN_TERMS_CALCULATION_ERROR';

export const GET_LOAN_SCHEDULE = 'globalcredit/loan/GET_LOAN_SCHEDULE';
export const GET_LOAN_SCHEDULE_SUCCESS =
  'globalcredit/loan/GET_LOAN_SCHEDULE_SUCCESS';
export const GET_LOAN_SCHEDULE_ERROR =
  'globalcredit/loan/GET_LOAN_SCHEDULE_ERROR';

export const approveLoan = (payload, cb) => ({
  type: LOAN_APPROVE,
  payload,
  cb,
});
export const approveLoanFulfilled = (payload, cb) => ({
  type: LOAN_APPROVED,
  payload,
  cb,
});
export const approveLoanError = payload => ({
  type: LOAN_APPROVE_ERROR,
  payload,
});

export const fetchLoan = () => ({
  type: LOAN_FETCH,
});
export const fetchLoanFulfilled = payload => ({
  type: LOAN_FETCHED,
  payload,
});
export const fetchLoanError = payload => ({
  type: LOAN_FETCH_ERROR,
  payload,
});

export const fetchLoanByPublicId = payload => ({
  type: LOAN_BY_PUBLIC_ID_FETCH,
  payload,
});
export const fetchLoanByPublicIdFulfilled = payload => ({
  type: LOAN_BY_PUBLIC_ID_FETCHED,
  payload,
});
export const fetchLoanByPublicIdError = payload => ({
  type: LOAN_BY_PUBLIC_ID_FETCH_ERROR,
  payload,
});

export const storeTransferInfo = (payload, cb) => ({
  type: STORE_TRANSFER_INFO,
  payload,
  cb,
});
export const storeTransferInfoFulfilled = payload => ({
  type: TRANSFER_INFO_STORED,
  payload,
});
export const storeTransferInfoError = payload => ({
  type: STORE_TRANSFER_INFO_ERROR,
  payload,
});

export const getLoanDocuments = () => ({
  type: GET_LOAN_DOCUMENTS,
});
export const getLoanDocumentsFulfilled = payload => ({
  type: GET_LOAN_DOCUMENTS_SUCCESS,
  payload,
});
export const getLoanDocumentsError = payload => ({
  type: GET_LOAN_DOCUMENTS_ERROR,
  payload,
});

export const getPersonalInfo = () => ({
  type: PERSONAL_INFO_GET,
});
export const getPersonalInfoFulfilled = payload => ({
  type: PERSONAL_INFO_GET_SUCCESS,
  payload,
});
export const getPersonalInfoError = payload => ({
  type: PERSONAL_INFO_GET_ERROR,
  payload,
});

export const updatePersonalInfo = (payload, cb) => ({
  type: PERSONAL_INFO_UPDATE,
  payload,
  cb,
});
export const updatePersonalInfoFulfilled = payload => ({
  type: PERSONAL_INFO_UPDATED,
  payload,
});
export const updatePersonalInfoError = payload => ({
  type: PERSONAL_INFO_UPDATE_ERROR,
  payload,
});

export const getLoanTermsCalculation = payload => ({
  type: GET_LOAN_TERMS_CALCULATION,
  payload,
});

export const getLoanTermsCalculationFulfilled = payload => ({
  type: GET_LOAN_TERMS_CALCULATION_SUCCESS,
  payload,
});
export const getLoanTermsCalculationError = payload => ({
  type: GET_LOAN_TERMS_CALCULATION_ERROR,
  payload,
});

export const getLoanSchedule = payload => ({
  type: GET_LOAN_SCHEDULE,
  payload,
});

export const getLoanScheduleFulfilled = payload => ({
  type: GET_LOAN_SCHEDULE_SUCCESS,
  payload,
});
export const getLoanScheduleError = payload => ({
  type: GET_LOAN_SCHEDULE_ERROR,
  payload,
});

const initialState = {
  data: {},
  loading: false,
  documents: [],
  termsCalculation: {},
  termsCalculationLoading: false,
  schedule: {},
  scheduleLoading: false,
};

export default function reducer(state = initialState, action) {
  switch (action.type) {
    case LOAN_APPROVE:
      return {
        ...state,
        loading: true,
      };
    case LOAN_APPROVED:
      return {
        ...state,
        loading: false,
      };
    case LOAN_APPROVE_ERROR:
      return {
        ...state,
        error: action.payload,
        loading: false,
      };

    case GET_LOAN_DOCUMENTS:
      return {
        ...state,
        loading: true,
      };
    case GET_LOAN_DOCUMENTS_SUCCESS:
      return {
        ...state,
        documents: action.payload.data,
        loading: false,
      };
    case GET_LOAN_DOCUMENTS_ERROR:
      return {
        ...state,
        error: action.payload,
        loading: false,
      };

    case PERSONAL_INFO_GET:
      return {
        ...state,
        loading: true,
      };
    case PERSONAL_INFO_GET_SUCCESS:
      return {
        ...state,
        data: {
          ...action.payload.data,
          citizen: {
            ...action.payload.data.citizen,
            passports: _.keyBy(action.payload.data.citizen.passports, 'type'),
          },
        },
        loading: false,
      };
    case PERSONAL_INFO_GET_ERROR:
      return {
        ...state,
        error: action.payload,
        loading: false,
      };

    case PERSONAL_INFO_UPDATE:
      return {
        ...state,
        loading: true,
      };
    case PERSONAL_INFO_UPDATED:
      return {
        ...state,
        loading: false,
      };
    case PERSONAL_INFO_UPDATE_ERROR:
      return {
        ...state,
        error: action.payload,
        loading: false,
      };

    case LOAN_FETCH:
      return {
        ...state,
        loading: true,
      };
    case LOAN_FETCHED:
      return {
        ...state,
        data: action.payload.data,
        loading: false,
      };
    case LOAN_FETCH_ERROR:
      return {
        ...state,
        error: action.payload,
        loading: false,
      };

    case LOAN_BY_PUBLIC_ID_FETCH:
      return {
        ...state,
        loading: true,
      };
    case LOAN_BY_PUBLIC_ID_FETCHED:
      return {
        ...state,
        data: action.payload.data,
        loading: false,
      };
    case LOAN_BY_PUBLIC_ID_FETCH_ERROR:
      return {
        ...state,
        error: action.payload,
        loading: false,
      };

    case STORE_TRANSFER_INFO:
      return {
        ...state,
        transferInfoLoading: true,
      };
    case TRANSFER_INFO_STORED:
      return {
        ...state,
        data: action.payload.data,
        transferInfoLoading: false,
      };
    case STORE_TRANSFER_INFO_ERROR:
      return {
        ...state,
        transferInfoError: action.payload,
        transferInfoLoading: false,
      };

    case GET_LOAN_TERMS_CALCULATION:
      return {
        ...state,
        termsCalculationLoading: true,
      };
    case GET_LOAN_TERMS_CALCULATION_SUCCESS:
      return {
        ...state,
        termsCalculation: action.payload.data,
        termsCalculationLoading: false,
      };
    case GET_LOAN_TERMS_CALCULATION_ERROR:
      return {
        ...state,
        error: action.payload,
        termsCalculationLoading: false,
      };

    case GET_LOAN_SCHEDULE:
      return {
        ...state,
        scheduleLoading: true,
      };
    case GET_LOAN_SCHEDULE_SUCCESS:
      return {
        ...state,
        schedule: action.payload.data,
        scheduleLoading: false,
      };
    case GET_LOAN_SCHEDULE_ERROR:
      return {
        ...state,
        error: action.payload,
        scheduleLoading: false,
      };
    default:
      return state;
  }
}

export const approveLoanEpic = action$ =>
  action$.pipe(
    ofType(LOAN_APPROVE),
    mergeMap(action =>
      from(
        axios.post('loans/approve', action.payload, {
          headers: getSuuidHeader(),
        })
      ).pipe(
        mergeMap(response =>
          of(approveLoanFulfilled(response.data, action.cb), wizardErrorClear())
        ),
        catchError(error =>
          of(
            wizardError(error.response.data.error),
            approveLoanError(error.response && error.response.data.error)
          )
        )
      )
    )
  );

export const approvedLoanEpic = action$ =>
  action$.pipe(
    ofType(LOAN_APPROVED),
    mergeMap(action => {
      action.cb();
      return empty();
    })
  );

export const getLoanDocumentsEpic = action$ =>
  action$.pipe(
    ofType(GET_LOAN_DOCUMENTS),
    mergeMap(() =>
      from(
        axios.get('loans/get-documents', {
          headers: getSuuidHeader(),
        })
      ).pipe(
        mergeMap(response => {
          return of(
            getLoanDocumentsFulfilled(response.data),
            wizardErrorClear()
          );
        }),
        catchError(error =>
          of(
            wizardError(error.response.data.error),
            getLoanDocumentsError(error.response && error.response.data.error)
          )
        )
      )
    )
  );

export const getPersonalInfoEpic = action$ =>
  action$.pipe(
    ofType(PERSONAL_INFO_GET),
    mergeMap(() =>
      from(
        axios.get('loans/personal-info', {
          headers: getSuuidHeader(),
        })
      ).pipe(
        mergeMap(response => {
          return of(
            getPersonalInfoFulfilled(response.data),
            wizardErrorClear()
          );
        }),
        catchError(error =>
          of(
            wizardError(error.response.data.error),
            getPersonalInfoError(error.response && error.response.data.error)
          )
        )
      )
    )
  );

export const updatePersonalInfoEpic = action$ =>
  action$.pipe(
    ofType(PERSONAL_INFO_UPDATE),
    mergeMap(action =>
      from(
        axios.put('loans/personal-info', action.payload, {
          headers: getSuuidHeader(),
        })
      ).pipe(
        mergeMap(response => {
          action.cb();
          return of(
            updatePersonalInfoFulfilled(response.data),
            wizardErrorClear()
          );
        }),
        catchError(error =>
          of(
            wizardError(error.response.data.error),
            updatePersonalInfoError(error.response && error.response.data.error)
          )
        )
      )
    )
  );

export const fetchLoanEpic = action$ =>
  action$.pipe(
    ofType(LOAN_FETCH),
    mergeMap(() =>
      from(
        axios.get('loans', {
          headers: getSuuidHeader(),
        })
      ).pipe(
        mergeMap(response => {
          return of(fetchLoanFulfilled(response.data), wizardErrorClear());
        }),
        catchError(error =>
          of(
            wizardError(error.response.data.error),
            fetchLoanError(error.response && error.response.data.error)
          )
        )
      )
    )
  );

export const fetchLoanByPublicIdEpic = action$ =>
  action$.pipe(
    ofType(LOAN_BY_PUBLIC_ID_FETCH),
    mergeMap(action =>
      axios
        .get(`loans/fetch?public_id=${action.payload}`)
        .then(response => {
          return fetchLoanByPublicIdFulfilled(response.data);
        })
        .catch(error => {
          return fetchLoanByPublicIdError(
            error.response && error.response.data.error
          );
        })
    )
  );

export const storeTransferInfoEpic = action$ =>
  action$.pipe(
    ofType(STORE_TRANSFER_INFO),
    mergeMap(action =>
      from(
        axios.post(`loans/${action.payload.route}`, action.payload, {
          headers: getSuuidHeader(),
        })
      ).pipe(
        mergeMap(response => {
          action.cb();
          return of(
            storeTransferInfoFulfilled(response.data),
            wizardErrorClear()
          );
        }),
        catchError(error =>
          of(
            wizardError(error.response.data.error),
            storeTransferInfoError(error.response && error.response.data.error)
          )
        )
      )
    )
  );

export const getLoanTermsCalculationEpic = action$ =>
  action$.pipe(
    ofType(GET_LOAN_TERMS_CALCULATION),
    mergeMap(action =>
      axios
        .post('loans/calculate-monthly-payment', action.payload, {
          headers: getSuuidHeader(),
        })
        .then(response => {
          return getLoanTermsCalculationFulfilled(response.data);
        })
        .catch(error => {
          return getLoanTermsCalculationError(
            error.response && error.response.data.error
          );
        })
    )
  );

export const getLoanScheduleEpic = action$ =>
  action$.pipe(
    ofType(GET_LOAN_SCHEDULE),
    mergeMap(action =>
      axios
        .post('loans/calculate-schedule', action.payload, {
          headers: getSuuidHeader(),
        })
        .then(response => {
          return getLoanScheduleFulfilled(response.data);
        })
        .catch(error => {
          return getLoanScheduleError(
            error.response && error.response.data.error
          );
        })
    )
  );
