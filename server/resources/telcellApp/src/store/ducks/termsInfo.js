import { ofType } from 'redux-observable';
import { catchError, mergeMap } from 'rxjs/operators';
import { API_URL_OTCL } from '../../config';
import axios from '../../config/axios';
import { from, of } from 'rxjs';
import { wizardError, wizardErrorClear } from './wizard';

export const FETCH_TERMS_INFO = 'globalcredit/terms_info/FETCH_TERMS_INFO';
export const TERMS_INFO_FETCHED = 'globalcredit/terms_info/TERMS_INFO_FETCHED';
export const ERROR_FETCHING_TERMS_INFO =
  'globalcredit/terms_info/ERROR_FETCHING_TERMS_INFO';

export const fetchTermsInfo = payload => ({
  type: FETCH_TERMS_INFO,
  payload,
});

export const fetchTermsInfoFulfilled = payload => ({
  type: TERMS_INFO_FETCHED,
  payload,
});

export const fetchTermsInfoError = payload => ({
  type: ERROR_FETCHING_TERMS_INFO,
  payload,
});

const initialState = {
  data: null,
  loading: false,
};

export default function reducer(state = initialState, action) {
  switch (action.type) {
    case FETCH_TERMS_INFO:
      return {
        loading: true,
      };
    case TERMS_INFO_FETCHED:
      return {
        data: action.payload.data,
        loading: false,
      };
    case ERROR_FETCHING_TERMS_INFO:
      return {
        error: action.payload,
        loading: false,
      };
    default:
      return state;
  }
}

export const termInfoEpic = action$ =>
  action$.pipe(
    ofType(FETCH_TERMS_INFO),
    mergeMap(action =>
      from(
        axios.get('fetch-terms-info', {
          baseURL: API_URL_OTCL,
          params: action.payload,
        })
      ).pipe(
        mergeMap(response => {
          return of(fetchTermsInfoFulfilled(response.data), wizardErrorClear());
        }),
        catchError(error =>
          of(
            wizardError(error.response.data.error),
            fetchTermsInfoError(error.response && error.response.data.error)
          )
        )
      )
    )
  );
