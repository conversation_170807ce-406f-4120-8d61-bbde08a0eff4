import { ofType } from 'redux-observable';
import { mergeMap, catchError } from 'rxjs/operators';
import { of, from } from 'rxjs';
import { getSuuidHeader } from '../../helpers/auth';
import { wizardError, wizardErrorClear } from './wizard';
import axios from '../../config/axios';

export const TRANSFER_TYPES_FETCH =
  'globalcredit/transfer_types/TRANSFER_TYPES_FETCH';
export const TRANSFER_TYPES_FETCHED =
  'globalcredit/transfer_types/TRANSFER_TYPES_FETCHED';
export const TRANSFER_TYPES_ERROR =
  'globalcredit/transfer_types/TRANSFER_TYPES_ERROR';

export const fetchTransferTypes = document_number => ({
  type: TRANSFER_TYPES_FETCH,
  document_number,
});

export const fetchTransferTypesFulfilled = payload => ({
  type: TRANSFER_TYPES_FETCHED,
  payload,
});

export const fetchTransferTypesError = payload => ({
  type: TRANSFER_TYPES_ERROR,
  payload,
});

const initialState = {
  data: null,
  loading: false,
};

export default function reducer(state = initialState, action) {
  switch (action.type) {
    case TRANSFER_TYPES_FETCH:
      return {
        loading: true,
      };
    case TRANSFER_TYPES_FETCHED:
      return {
        data: action.payload.data,
        loading: false,
      };
    case TRANSFER_TYPES_ERROR:
      return {
        error: action.payload,
        loading: false,
      };
    default:
      return state;
  }
}

export const transferTypesEpic = action$ =>
  action$.pipe(
    ofType(TRANSFER_TYPES_FETCH),
    mergeMap(() =>
      from(
        axios.get('transfer-types', {
          headers: getSuuidHeader(),
        })
      ).pipe(
        mergeMap(response => {
          return of(
            fetchTransferTypesFulfilled(response.data),
            wizardErrorClear()
          );
        }),
        catchError(error =>
          of(
            wizardError(error.response.data.error),
            fetchTransferTypesError(error.response && error.response.data.error)
          )
        )
      )
    )
  );
