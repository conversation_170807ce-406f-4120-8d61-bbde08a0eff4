// Typography
$main-font-family: 'DejaVu Sans Book Arm', 'sans-serif', 'Arial';

// Colors
// Static pages colors
$title-color: #3589af;
$button-color: #f87322;
$upay-button-color: #7ebc0a;
$easypay-button-color: #8bc34a;
$fastshift-button-color: #6c26db;
$main: #8f3021;
$main-transparent: #a65c4ff2;
$dark-gray: #555555;
$modal-bg-color: #f5f7f8;
$border-color: #becbd1;
$gc-red-color: #8f3327;
$black-transparent: #00000061;
$pink: #ff9696;
$darker-skin: #ffdede;

// telcellAPP colors
$white: #fff;
$cloud-white: #f7f7f7;
$white-smoke: #f8f7f7;
$smoke-gray: #f2f2f2;
$lighty-gray: #e3e3e3;
$light-gray: #f2f2f2;
$lighter-gray: #d3d3d3;
$gray: #dedede;
$dark-gray: #707070;
$darky-gray: #b1b1b1;
$box-shadow-gray: #d4d9e83d;
$wet-asphalt: #a8a3b8;
$darker-gray: #5a5a5a;
$mercury-gray: #e5e5e5;
$skin: #fff6f6;
$darker-skin: #ffdede;
$light-green: #49aa36;
$lighty-green: #478f21;
$pear: #cfeb29;
$green: green;
$pink: #ff9696;
$red: #ff0000;
$dark-red: #c83d34;
$barn-red: #934136;
$blue: #0000ff;
$azure-radiance: #007aff;
$light-blue: #4dc0dc;
$lighter-blue: #58c4ff;
$cyan-blue: #d0f0ff;
$ocean-blue: #0084e2;
$deep-sky-blue: #06bdf4;
$sky-blue: #2699fb;
$darky-blue: #00478d;
$purple-blue: #303a90;
$smoke-blue: #34495e;
$jacksons-purple: #3b3e7b;
$orange: #c9977f;
$light-orange: #ffb858;
$dark-orange: #e8a346;
$web-orange: #ffa500;
$black-with-opacity: rgba(0, 0, 0, 0.16);
$black: #000;
$light-black: #141417;
$crail: #a75b4a;
$almond: #e8e5de;

$main: #8f3021;
$secondary: #2e241d;
$main-text-color: #b3b5b0;
$secondary-text-color: #2e241d;
$wizard-secondary-color: #ab4131;
$telcell-orange: #e96c2f;
