import * as Yup from 'yup';
import i18n from '../../i18n';
import './validators';

export const smsValidationCodeSchema = Yup.object().shape({
  code: Yup.string()
    .required()
    .code(),
  termsAccepted: Yup.bool()
    .test('termsAccepted', i18n.t('validator.terms'), value => value === true)
    .required(i18n.t('validator.terms')),
  agreement: Yup.bool()
    .test('termsAccepted', i18n.t('validator.terms'), value => value === true)
    .required(i18n.t('validator.terms')),
  loanPdfsConfirmed: Yup.bool()
    .test(
      'loanPdfsConfirmed',
      i18n.t('validator.terms'),
      value => value === true
    )
    .required(i18n.t('validator.terms')),
});

export const smsGetCodeSchema = Yup.object().shape({
  termsAccepted: Yup.bool()
    .test('termsAccepted', i18n.t('validator.terms'), value => value === true)
    .required(i18n.t('validator.terms')),
  agreement: Yup.bool()
    .test('termsAccepted', i18n.t('validator.terms'), value => value === true)
    .required(i18n.t('validator.terms')),
  loanPdfsConfirmed: Yup.bool()
    .test(
      'loanPdfsConfirmed',
      i18n.t('validator.terms'),
      value => value === true
    )
    .required(i18n.t('validator.terms')),
});
