import * as Yup from 'yup';
import i18n from '../../i18n';
import { isPassport, isPhone } from '../index';

Yup.addMethod(Yup.string, 'code', function() {
  return this.test('code', i18n.t('validator.code'), function(value) {
    if (value) {
      return value.length === 4;
    }

    return true;
  });
});

Yup.addMethod(Yup.string, 'vehicleNumber', function() {
  return this.test('vehicleNumber', i18n.t('validator.vehicleNumber'), function(
    value
  ) {
    if (value) {
      return value.length === 7;
    }

    return true;
  });
});

Yup.addMethod(Yup.string, 'techPassport', function() {
  return this.test('techPassport', i18n.t('validator.techPassport'), function(
    value
  ) {
    if (value) {
      return value.length === 8;
    }

    return true;
  });
});

Yup.addMethod(Yup.string, 'tradeAmount', function() {
  return this.test('tradeAmount', i18n.t('validator.tradeAmount'), function(
    value
  ) {
    if (value) {
      return value.length >= 7 && value.length <= 13;
    }

    return true;
  });
});

Yup.addMethod(Yup.string, 'phone', function() {
  return this.test('phone', i18n.t('validator.phone'), function(value) {
    return isPhone(value);
  });
});

Yup.addMethod(Yup.string, 'passport', function() {
  return this.test('passport', i18n.t('validator.passport'), function(value) {
    if (value && isPassport(value)) {
      return value.length === 9;
    }

    return true;
  });
});
