import * as Yup from 'yup';
import i18n from '../../i18n';
import './validators';

export const wizardLoanAmountSchema = (aprName = 'aprApprovalCheckbox') => {
  return Yup.object().shape({
    isHuman: Yup.bool()
      .test('isHuman', i18n.t('validator.captcha'), value => value === true)
      .required(i18n.t('validator.captcha')),
    [aprName]: Yup.bool().when('aprEnabled', {
      is: true,
      then: Yup.bool()
        .test(aprName, i18n.t('validator.terms'), value => value === true)
        .required(i18n.t('validator.terms')),
    }),
    apr: Yup.string().when('aprEnabled', {
      is: true,
      then: Yup.string()
        .required(i18n.t('validator.apr_approval_input_required'))
        .test('apr', i18n.t('validator.apr_approval_input'), function(value) {
          const v = value && +value.replace(',', '.');

          return v === this.parent.actualApr;
        }),
    }),
  });
};

export const tradeVehicleSchema = Yup.object().shape({
  vehicleNumber: Yup.string()
    .required()
    .vehicleNumber(),
  techPassport: Yup.string()
    .required()
    .techPassport(),
  tradeAmount: Yup.string()
    .required()
    .tradeAmount(),
});
