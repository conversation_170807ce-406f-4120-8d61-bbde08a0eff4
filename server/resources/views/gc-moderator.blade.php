<!DOCTYPE html>
<html lang='en'>
    <head>
        <meta charset='utf-8'>
        <meta http-equiv='X-UA-Compatible' content='IE=edge'>
        <meta name='viewport' content='width=device-width, initial-scale=1'>

        <title>Globalcredit</title>

        <!-- Fonts -->
        <link href='https://fonts.googleapis.com/css?family=Raleway:100,600' rel='stylesheet' type='text/css'>
        <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css" integrity="sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm" crossorigin="anonymous">
        <!-- Styles -->
        <style>
            html, body {
                background-color: #fff;
                color: #636b6f;
                /* font-family: 'Robotto', sans-serif; */
                font-weight: 100;
                height: 100vh;
                margin: 0;
            }
            .card {
                box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);
                transition: 0.3s;
                width: 100%;
            }

            .card:hover {
                box-shadow: 0 8px 16px 0 rgba(0,0,0,0.2);
            }
            h2, .loan-info {
                padding-left: 15px;
            }

            .rejected {
                color: red;
            }

            .confirmed {
                color: green;
            }

            .webcam-photo {
                width: 300px;
            }

            .user-info {
                border-bottom: 2px solid #dee2e6;
                padding: 8px 0 8px 15px;
                padding-bottom: 10px;
            }

            .personal-info {
                width: 200px;
                padding-right: 15px;
                margin-top: 85px;
            }
            .personal-info p {
                border-bottom: 2px dotted #dee2e6;
                padding-bottom: 10px;
            }

            .right-block {
                margin-left: 30px;
                margin-top: 80px;
            }

            .ekeng-btn, .kairos-btn, .filter-button {
                background-color: #28a745;
                border-color: #28a745;
                color: #fff;
                cursor: pointer;
                padding: .375rem .75rem;
                font-size: 1rem;
                line-height: 1.5;
                border-radius: .25rem;
                margin: 10px 0;
            }

            .ekeng-photo {
                margin-left: 20px;
            }

            .zoom label {
                font-size: 20px;
                display: block
            }

            .zoom .zoom-btn {
                cursor: pointer;
            }


            .bg-green {
                background: #008000cc;
                color: black;
            }

            .bg-red {
                background: #ff0000a3;
                color: black;
            }

            .hidden {
                visibility: hidden;
            }

            .mr-10 {
                margin-right: 10px;
            }
            .d-flex {
                display: flex;
            }

            .filter {
                padding: 15px;
                border: 1px solid lightskyblue;
                margin: 15px;
            }
            .filter p {
                margin: 0;
                font-weight: 600;
            }

            #video-urls {
                font-weight: 500;
                margin-top: 10px;
            }

            #video-urls a {
                color: dimgray;
            }
        </style>
        <script src="https://code.jquery.com/jquery-3.2.1.slim.min.js" integrity="sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMVNA/GpGFF93hXpG5KkN" crossorigin="anonymous"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js" integrity="sha384-ApNbgh9B+Y1QKtv3Rn7W3mgPxhU9K/ScQsAP7hUibX39j7fakFPskvXusvfa0b4Q" crossorigin="anonymous"></script>
        <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js" integrity="sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl" crossorigin="anonymous"></script>
        <script src="https://rawgit.com/moment/moment/2.2.1/min/moment.min.js"></script>
    </head>
    <body>
        <div id='content' class='content'>
            @if (isset($loans))
                <h2>
                    Loans
                </h2>
                <div class="loan-info">
                    <h4 class="confirmed">
                        Confirmed - {{$confirmed}}
                    </h4>
                    <h4 class="rejected">
                        Rejected - {{$rejected}}
                    </h4>
                </div>
                <div class="filter">
                    <div class="d-flex">
                        <div class="date d-flex">
                            <div>
                                <p><label for="from">Start Date:</label></p>
                                <input type="date" name="from" class="mr-10 form-control" value=""/>
                            </div>
                            <div class="ml-2">
                                <p><label for="to">End Date:</label></p>
                                <input type="date" name="to" class="mr-10 form-control" value=""/>
                            </div>
                        </div>
                        <div class="ml-2">
                            <p><label for="status">Status:</label></p>
                            <select class="custom-select" id="status">
                                <option selected value="">-</option>
                                <option value="CONFIRMED">Confirmed</option>
                                <option value="REJECTED"">Rejected</option>
                            </select>
                        </div>
                        <div class="ml-2">
                            <p><label for="loan-status">Sort:</label></p>
                            <select class="custom-select" id="sort">
                                <option selected value="desc">Descending</option>
                                <option value="asc">Ascending </option>
                            </select>
                        </div>
                    </div>
                    <button type='submit' class='filter-button' onclick="useFilter()">Use Filter</button>
                </div>
                @foreach($loans as $index=>$r)
                    <div class='user-info card'>
                    <div style='display: flex; flex-direction:row;'>
                            <div class="personal-info">
                                <h4>
                                    {{$index+1}}.
                                </h4>
                                <h3>
                                    Loan ID -
                                    <i>
                                        {{ $r->id }}
                                    </i>
                                </h3>
                                <p>
                                    Created Date:
                                    <br/>
                                    <b>
                                        {{$r->created_at->setTimezone('Asia/Yerevan') ?? null}}
                                    </b>
                                </p>
                                <p>
                                    Confirmed Date:
                                    <br>
                                    <b>
                                        {{$r->confirmed_at ? $r->confirmed_at->setTimezone('Asia/Yerevan') : null}}
                                    </b>
                                </p>
                                <p>
                                    Amount:
                                    <b>
                                        {{$r->amount ?? null}}
                                    </b>
                                </p>
                                <p>
                                    Status
                                    <b>
                                        {{$r->status ?? null}}
                                    </b>
                                </p>
                                <p>
                                    Payment Type
                                    <b>
                                        {{
                                            $payment_types[$r->loan_security->loan->payment_type ?? null] ?? $r->loan_security->loan->payment_type ?? null
                                        }}
                                    </b>
                                </p>
                            </div>
                            <div class="personal-info right-block">
                                <div class="">
                                    <h3>&nbsp;</h3>
                                    <b>
                                        &nbsp;
                                    </b>
                                    <h3>
                                        Moderator
                                    </h3>
                                </div>
                                <p>
                                    ID:
                                    <b>
                                        {{
                                            $r->moderator()->first()->id
                                        }}
                                    </b>
                                </p>
                                <p>
                                    First Name:
                                    <b>
                                        {{
                                            $r->moderator()->first()->first_name
                                        }}
                                    </b>
                                </p>
                                <p>
                                    Last Name:
                                    <b>
                                        {{
                                            $r->moderator()->first()->first_name
                                        }}
                                    </b>
                                </p>
                                <p>
                                    Notes:
                                    <b>
                                        {{
                                            $r->moderator()->first()->pivot->notes
                                        }}
                                    </b>
                                </p>
                                <p>
                                    Assigned at:
                                    <br/>
                                    <b>
                                        {{
                                            $r->moderator()->first()->pivot->assigned_at
                                        }}
                                    </b>
                                </p>
                                <p>
                                    {{
                                        $r->moderator()->first()->pivot->confirmed_at ? 'Confirmed at' : 'Rejected at'
                                    }}
                                    <br/>
                                    <b>
                                        {{
                                            $r->moderator()->first()->pivot->confirmed_at ?? $r->moderator()->first()->pivot->rejected_at
                                        }}
                                    </b>
                                </p>
                            </div>
                            <div class='photos'>
                                <div class='zoom'>
                                    <label>
                                        Zoom:
                                    </label>
                                    <input
                                        class='zoom-btn'
                                        type='range'
                                        min='100'
                                        max='800'
                                        value='300'
                                        oninput='zoom(this.value, {{$index}})'
                                    >
                                    <div>
                                        <button type='submit' class='ekeng-btn' id={{$r->loan_security->ssn}}>Ekeng</button>
                                        <button
                                            type='submit'
                                            class='filter-button'
                                            id={{$r->id}} onclick="getVideos({{$r->id}})">
                                            Get Videos
                                        </button>
                                    </div>
                                    <div id={{'video-urls-'.$r->id}}></div>
                                </div>
                                <div>
                                    <img
                                        id={{'webcam-photo-'.$index}}
                                        class='webcam-photo'
                                        src={{$r->loan_security->citizen_face_recognitions()->latest('created_at')->first()->full_image_path}}
                                    />
                                    <img class='ekeng-photo' id={{'img-'.$r->loan_security->ssn}} />
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            @endif
        </div>
        <script>
            const url = new URL(window.location.href);
            const from = url.searchParams.get('from') || moment().utc().format('YYYY-MM-DD');
            const to =  url.searchParams.get('to') || moment().utc().add('days', 1).format('YYYY-MM-DD');
            const status = url.searchParams.get('status') || '';

            document.querySelector('input[name="from"]').value = from;
            document.querySelector('input[name="to"]').value = to;
            document.getElementById('status').value = status;

            function zoom(value, index) {
                const image = document.getElementById('webcam-photo-' + index);
                image.style.width = value + 'px';
            }

            document.getElementById('content').addEventListener('click', function(e) {
                const classes = [].slice.apply(e.target.classList);
                const ssn = e.target.id;

                if (classes.indexOf('ekeng-btn') !== -1) {
                    const url = window.location.origin + '/gc-view/recognition' + '/ekeng?ssn='+ssn;

                    fetch(url)
                    .then(response=>response.json())
                    .then((response) => {
                        document.getElementById('img-'+ssn).src = 'data:image/png;base64,' + response.image;
                    })
                    .then((err) => {
                        console.log(err);
                    });
                }
            });

            function useFilter () {
                const from = document.querySelector('input[name="from"]').value;
                const to = document.querySelector('input[name="to"]').value

                // statusElm = status element
                const statusElm = document.getElementById('status');
                const status = statusElm.options[statusElm.selectedIndex].value;

                const sortElm = document.getElementById('sort');
                const sort = sortElm.options[sortElm.selectedIndex].value;

                const query = `?from=${from}&to=${to}&status=${status}&sort=${sort}`;
                const url = `${window.location.origin}${window.location.pathname}${query}`;

                window.location.href = url;
            }

            function getVideos(loanId) {
                const url = window.location.origin + '/gc-view/moderator/videos?loan_id=' + loanId;

                fetch(url)
                    .then(response=>response.json())
                    .then((response) => {
                        let linksHTML = '';

                        for (let i = 0; i < response.urls.length; i++) {
                            const element = response.urls[i];

                            linksHTML += `${i+1}. <a href="${element.url}" target="_blank">Video ${i+1}</a><br/>`;
                        }

                        document.getElementById(`video-urls-${loanId}`).innerHTML = linksHTML;
                    })
                    .then((err) => {
                        console.log(err);
                    });
            }
        </script>
    </body>
</html>
