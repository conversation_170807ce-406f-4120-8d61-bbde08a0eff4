<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='utf-8'>
    <meta http-equiv='X-UA-Compatible' content='IE=edge'>
    <meta name='viewport' content='width=device-width, initial-scale=1'>

    <title>Globalcredit</title>

    <!-- Fonts -->
    <link href='https://fonts.googleapis.com/css?family=Raleway:100,600' rel='stylesheet' type='text/css'>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css" integrity="sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm" crossorigin="anonymous">
    <!-- Styles -->
    <style>
        html, body {
            background-color: #fff;
            color: #636b6f;
            /* font-family: 'Robotto', sans-serif; */
            font-weight: 100;
            height: 100vh;
            margin: 0;
        }
        .card {
            box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);
            transition: 0.3s;
            width: 100%;
        }

        .card:hover {
            box-shadow: 0 8px 16px 0 rgba(0,0,0,0.2);
        }
        h2 {
            padding-left: 15px;
        }
        .webcam-photo {
            width: 300px;
        }

        .user-info {
            border-bottom: 2px solid #dee2e6;
            padding: 8px 0 8px 15px;
            padding-bottom: 10px;
        }

        .personal-info {
            width: 200px;
            padding-right: 15px;
            margin-top: 85px;
        }
        .personal-info p {
            border-bottom: 2px dotted #dee2e6;
            padding-bottom: 10px;
        }

        .ekeng-btn, .kairos-btn, .filter-button {
            background-color: #28a745;
            border-color: #28a745;
            color: #fff;
            cursor: pointer;
            padding: .375rem .75rem;
            font-size: 1rem;
            line-height: 1.5;
            border-radius: .25rem;
            margin: 10px 0;
        }

        .ekeng-photo {
            margin-left: 20px;
        }

        .zoom label {
            font-size: 20px;
            display: block
        }

        .zoom .zoom-btn {
            cursor: pointer;
        }


        .bg-green {
            background: #008000cc;
            color: black;
        }

        .bg-red {
            background: #ff0000a3;
            color: black;
        }

        .hidden {
            visibility: hidden;
        }

        .mr-10 {
            margin-right: 10px;
        }
        .d-flex {
            display: flex;
        }

        .filter {
            padding: 15px;
            border: 1px solid lightskyblue;
            margin: 15px;
        }
        .filter p {
            margin: 0;
            font-weight: 600;
        }
    </style>
    <script src="https://code.jquery.com/jquery-3.2.1.slim.min.js" integrity="sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMVNA/GpGFF93hXpG5KkN" crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js" integrity="sha384-ApNbgh9B+Y1QKtv3Rn7W3mgPxhU9K/ScQsAP7hUibX39j7fakFPskvXusvfa0b4Q" crossorigin="anonymous"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js" integrity="sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl" crossorigin="anonymous"></script>
</head>
<body>
<div id='content' class='content'>
    @if (isset($recognitions))
        <h2>
            Loans
        </h2>
        <div class="filter">
            <div class="d-flex">
                <div class="date d-flex">
                    <div>
                        <p><label for="from">Start Date:</label></p>
                        <input type="date" name="from" class="mr-10 form-control" value=""/>
                    </div>
                    <div class="ml-2">
                        <p><label for="to">End Date:</label></p>
                        <input type="date" name="to" class="mr-10 form-control" value=""/>
                    </div>
                </div>
                <div class="ml-2">
                    <p><label for="status">Status:</label></p>
                    <select class="custom-select" id="status">
                        <option selected value="">-</option>
                        <option value="MATCHED">Matched</option>
                        <option value="UNMATCHED"">Unmatched</option>
                    </select>
                </div>
                <div class="ml-2">
                    <p><label for="loan-status">Has Loan:</label></p>
                    <select class="custom-select" id="loan-status">
                        <option selected value="">-</option>
                        <option value="true">True</option>
                        <option value="false">False</option>
                    </select>
                </div>
                <div class="ml-2">
                    <p><label for="loan-status">Sort:</label></p>
                    <select class="custom-select" id="sort">
                        <option selected value="desc">Descending</option>
                        <option value="asc">Ascending </option>
                    </select>
                </div>
            </div>
            <button type='submit' class='filter-button' onclick="useFilter()">Use Filter</button>
        </div>
        @foreach($recognitions as $index=>$r)
            @php (array_push($docs, $r->loan_security->document_number))
            <div class='user-info card'>
                <div style='display: flex; flex-direction:row;'>
                    <div class="{{$r->liveness_rate > 0.5 && !$r->extended_liveness ? 'bg-red personal-info' : 'personal-info'}}">
                        <b>
                            {{$index+1}}.
                        </b>
                        <h3>
                            ID -
                            <i>
                                {{ $r->id }}
                            </i>
                        </h3>
                        <h5>
                            ID2 -
                            <i>
                                {{ $r->loan_security->id }}
                            </i>
                        </h5>
                        <p>
                            Date:
                            <b>
                                {{$r->created_at->setTimezone('Asia/Yerevan') ?? null}}
                            </b>
                        </p>
                        <p class='document'>
                            Doc №:
                            <b>
                                {{$r->loan_security->document_number ?? null}}
                            </b>
                        </p>
                        <p>
                            Contract №:
                            <b>
                                {{$r->loan_security->loan->contract_number ?? null}}
                            </b>
                        </p>
                        <p>
                            Amount:
                            <b>
                                {{$r->loan_security->loan->amount ?? null}}
                            </b>
                        </p>
                        <p>
                            Status
                            <b>
                                {{$r->loan_security->loan->status ?? null}}
                            </b>
                        </p>
                        <p>
                            Payment Type
                            <b>
                                {{
                                    $payment_types[$r->loan_security->loan->payment_type ?? null] ?? $r->loan_security->loan->payment_type ?? null
                                }}
                            </b>
                        </p>
                        <p>Status:
                            <b>
                                {{$r->status}}
                            </b>
                        </p>
                        <p>Similarity:
                            <b>
                                {{round($r->face_similarity, 2)}}
                            </b>
                        </p>
                        <p>Liveness:
                            <b>
                                {{round($r->liveness_rate, 2)}}
                            </b>
                        </p>
                    </div>
                    <div class="personal-info right-block">
                        <div class="hidden">
                            <b>
                                {{$index+1}}.
                            </b>
                            <h3>
                                ID -
                                <i>
                                    {{ $r->id }}
                                </i>
                            </h3>
                        </div>
                        <p>
                            Extended Liveness:
                            <b>
                                {{$r->extended_liveness ? 'true' : 'false'}}
                            </b>
                        </p>
                        <p>
                            Width:
                            <b>
                                {{$r->image_width}}
                            </b>
                        </p>
                        <p>
                            Height:
                            <b>
                                {{$r->image_height}}
                            </b>
                        </p>
                        <p>
                            Quality:
                            <b>
                                {{$r->quality}}
                            </b>
                        </p>
                        <p>
                            Head Ratio Width:
                            <b>
                                {{round($r->head_ratio_width, 2)}}
                            </b>
                        </p>
                        <p>
                            Head Ratio Height:
                            <b>
                                {{round($r->head_ratio_height, 2)}}
                            </b>
                        </p>
                        <p>
                            Eye Distance:
                            <b>
                                {{$r->eye_distance}}
                            </b>
                        </p>
                    </div>
                    <div class='photos'>
                        <div class='zoom'>
                            <label>
                                Zoom:
                            </label>
                            <input
                                    class='zoom-btn'
                                    type='range'
                                    min='100'
                                    max='800'
                                    value='300'
                                    oninput='zoom(this.value, {{$index}})'
                            >
                            <div>
                                <button type='submit' class='ekeng-btn' id={{$r->loan_security->id}}>Ekeng</button>
                                @foreach($r->loan_security->video_archives ?? [] as $v)
                                    <b class="mr-10">
                                        {{ $v->archive_id }}
                                    </b>
                                @endforeach
                            </div>
                        </div>
                        <div>
                            <img
                                    id="{{'webcam-photo-'.$index}}"
                                    class='webcam-photo'
                                    src="{{$r->full_image_path}}"
                            />
                            <img class='ekeng-photo'
                                 src="{{$r->ekeng_photo->ekeng_full_image_path}}"
                                 id="{{'img-'.$r->loan_security->id}}"
                                 style="display: none; float: right; margin-left: 10px"
                            />
                        </div>
                    </div>
                </div>
            </div>
        @endforeach
    @endif
</div>
<p>
    UNIQUE: {{count(array_unique($docs))}}
</p>
<script>
    const url = new URL(window.location.href)
    const from = url.searchParams.get('from') || '';
    const to =  url.searchParams.get('to') || '';
    const status = url.searchParams.get('status') || '';
    const loanStatus = url.searchParams.get('ls') || '';

    document.querySelector('input[name="from"]').value = from;
    document.querySelector('input[name="to"]').value = to;
    document.getElementById('status').value = status;
    document.getElementById('loan-status').value = loanStatus;

    function zoom(value, index) {
        const image = document.getElementById('webcam-photo-' + index);
        image.style.width = value + 'px';
    }

    document.getElementById('content').addEventListener('click', function(e) {
        const classes = [].slice.apply(e.target.classList);
        const id = e.target.id;

        if (classes.indexOf('ekeng-btn') !== -1) {
            document.getElementById('img-'+id).style.display = 'block';
        }
    });

    function useFilter () {
        const from = document.querySelector('input[name="from"]').value;
        const to = document.querySelector('input[name="to"]').value

        // statusElm = status element
        const statusElm = document.getElementById('status');
        const status = statusElm.options[statusElm.selectedIndex].value;

        const loanStatusElm = document.getElementById('loan-status');
        const loanStatus = loanStatusElm.options[loanStatusElm.selectedIndex].value;

        const sortElm = document.getElementById('sort');
        const sort = sortElm.options[sortElm.selectedIndex].value;

        const query = `?from=${from}&to=${to}&status=${status}&ls=${loanStatus}&sort=${sort}`;
        const url = `${window.location.origin}${window.location.pathname}${query}`;

        window.location.href = url;
    }
</script>
</body>
</html>