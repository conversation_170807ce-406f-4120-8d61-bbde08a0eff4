<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='utf-8'>
    <meta http-equiv='X-UA-Compatible' content='IE=edge'>
    <meta name='viewport' content='width=device-width, initial-scale=1'>

    <title>Globalcredit</title>

    <!-- Fonts -->
    <link href='https://fonts.googleapis.com/css?family=Raleway:100,600' rel='stylesheet' type='text/css'>
    <!-- Styles -->
    <style>
        html, body {
            background-color: #fff;
            color: #636b6f;
            /* font-family: 'Robotto', sans-serif; */
            font-weight: 100;
            height: 100vh;
            margin: 0;
        }
        .card {
            box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);
            transition: 0.3s;
            width: 100%;
        }

        .card:hover {
            box-shadow: 0 8px 16px 0 rgba(0,0,0,0.2);
        }
        h2 {
            padding-left: 15px;
        }
        .webcam-photo {
            width: 300px;
        }

        .user-info {
            border-bottom: 2px solid #dee2e6;
            padding: 8px 0 8px 15px;
            padding-bottom: 10px;
        }

        .personal-info {
            width: 200px;
            padding-right: 15px;
            margin-top: 85px;
        }
        .personal-info p {
            border-bottom: 2px dotted #dee2e6;
            padding-bottom: 10px;
        }

        .ekeng-btn, .kairos-btn {
            background-color: #28a745;
            border-color: #28a745;
            color: #fff;
            cursor: pointer;
            padding: .375rem .75rem;
            font-size: 1rem;
            line-height: 1.5;
            border-radius: .25rem;
            margin: 10px 0;
        }

        .kairos-btn {
            display: none;
        }

        .ekeng-photo {
            margin-left: 20px;
        }

        .zoom label {
            font-size: 20px;
            display: block
        }

        .zoom .zoom-btn {
            cursor: pointer;
        }

        .kairos {
            display: flex;
            width: 350px !important;
        }

        .bg-green {
            background: #008000cc;
            color: black;
        }
    </style>
</head>
<body>
<div id='content' class='content'>
    @if (isset($recognitions))
        <h2>
            Loans
        </h2>
        @foreach($recognitions as $index=>$r)
            @php (array_push($docs, $r->loan_security->document_number))
            <div class='user-info card'>
                <div style='display: flex; flex-direction:row;'>
                    <div class='personal-info'>
                        <b>
                            {{$index+1}}.
                        </b>
                        <h3>
                            ID -
                            <i>
                                {{ $r->id }}
                            </i>
                        </h3>
                        <p>
                            Date:
                            <b>
                                {{$r->created_at->setTimezone('Asia/Yerevan') ?? null}}
                            </b>
                        </p>
                        <p class='document'>
                            Doc №:
                            <b>
                                {{$r->loan_security->document_number ?? null}}
                            </b>
                        </p>
                        <p>
                            Contract №:
                            <b>
                                {{$r->loan_security->loan->contract_number ?? null}}
                            </b>
                        </p>
                        <p>
                            Amount:
                            <b>
                                {{$r->loan_security->loan->amount ?? null}}
                            </b>
                        </p>
                        <p>
                            Status
                            <b>
                                {{$r->loan_security->loan->status ?? null}}
                            </b>
                        </p>
                        <p>
                            Payment Type
                            <b>
                                {{
                                    $payment_types[$r->loan_security->loan->payment_type ?? null] ?? $r->loan_security->loan->payment_type ?? null
                                }}
                            </b>
                        </p>
                        <p>Status:
                            <b>
                                {{$r->status}}
                            </b>
                        </p>
                        <p>Similarity:
                            <b>
                                {{round($r->face_similarity, 2)}}
                            </b>
                        </p>
                        <p>Liveness:
                            <b>
                                {{round($r->liveness_rate, 2)}}
                            </b>
                        </p>
                    </div>
                    <div class='personal-info kairos' id={{$r->image_path}}>
                        <div id={{'kairos-1'.$r->image_path}}>
                        </div>
                        <div id={{'kairos-2'.$r->image_path}} style='margin-left:20px;'>
                        </div>
                    </div>
                    <div class='photos'>
                        <div class='zoom'>
                            <label>
                                Zoom:
                            </label>
                            <input
                                    class='zoom-btn'
                                    type='range'
                                    min='100'
                                    max='800'
                                    value='300'
                                    oninput='zoom(this.value, {{$index}})'
                            >
                            <div>
                                <button type='submit' class='ekeng-btn' id={{$r->loan_security->id}}>Ekeng</button>
                                <button
                                        type='submit'
                                        class='kairos-btn'
                                        id={{$r->image_path}}>
                                    Get Karios
                                </button>
                            </div>
                        </div>
                        <div>
                            <img
                                    id="{{'webcam-photo-'.$index}}"
                                    class='webcam-photo'
                                    src="{{$r->full_image_path}}"
                            />
                            <img class='ekeng-photo'
                                 src="{{$r->ekeng_photo->ekeng_full_image_path}}"
                                 id="{{'img-'.$r->loan_security->id}}"
                                 style="display: none; float: right; margin-left: 10px"
                            />
                        </div>
                    </div>
                </div>
            </div>
        @endforeach
    @endif
</div>
<p>
    UNIQUE: {{count(array_unique($docs))}}
</p>
<script>
    window.onload = function() {
        const IDs = document.getElementsByClassName('kairos-btn');

        kariosRequest(Object.values(IDs));
    };

    function kariosRequest(buttons) {
        const maxReq = 50;
        let perMinute = 60000;

        for (let i = 0; i < buttons.length / maxReq; i++) {
            setTimeout(() => {
                const currImages = buttons.splice(0, maxReq).map(_ => _.id);
                console.log('part', i + 1);
                Promise.all(currImages.map(path => getKairosRequest(path)));
            },  perMinute * i);
        }
    }

    function zoom(value, index) {
        const image = document.getElementById('webcam-photo-' + index);
        image.style.width = value + 'px';
    }

    function getKairosRequest(path) {
        const url = 'https://gc-citizens-recognition.s3.eu-central-1.amazonaws.com/' + path
        const kairosUrl = 'https://api.kairos.com/detect'
        const link = new URL(window.location.href);
        const query = link.searchParams.get('minHeadScale');
        const minHeadScale = query || 0.0015;

        fetch(kairosUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'app_id': '756c9e79',
                'app_key': '67f35deeb116e3d028d4b9c08da8d8bf'
            },
            body: JSON.stringify({
                image: 'https://gc-citizens-recognition.s3.eu-central-1.amazonaws.com/' + path,
                selector:'liveness',
                minHeadScale
            })
        })
            .then(response => response.json())
            .then((response) => {
                addKairosResult(response, path);
                setCorrectResult(path);
            })
            .then((err) => {
                console.log(err);
            });
    }

    document.getElementById('content').addEventListener('click', function(e) {
        const classes = [].slice.apply(e.target.classList);
        const id = e.target.id;

        if (classes.indexOf('ekeng-btn') !== -1) {
            document.getElementById('img-'+id).style.display = 'block';
        }
    });

    function addKairosResult(response, path) {
        const text = `<b>1</b><p>Liveness: <b class="liveness">${ response.images[0].faces[0].attributes.liveness }</b></p>`;
        let additional = '';
        let attributes = '<b>2</b>'
        Object.entries(response.images[0].faces[0]).forEach(([key, value]) => {
            if (key === 'attributes') {
                Object.entries(value).forEach(([k, v]) => {
                    if (k !== 'gender') {
                        attributes +=`<p>${k}: <b>${v}</b></p>`
                    }
                })
            } else {
                additional += `<p>${key}: <b>${value}</b></p>`
            }
        });
        document.getElementById('kairos-1'+ path).innerHTML = text + additional;
        document.getElementById('kairos-2'+ path).innerHTML = attributes;
    }

    function setCorrectResult(id) {
        const table = document.getElementById(id);
        const liveness = +table.querySelector('.liveness').innerHTML;
        if (liveness > 0.2) {
            table.classList.add('bg-green');
        }
    }
</script>
</body>
</html>