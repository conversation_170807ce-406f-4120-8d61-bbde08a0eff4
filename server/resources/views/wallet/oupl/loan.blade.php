@extends('wallet.layout')

@section('script')
    const amount = "{!! $credit['amount'] ?? 0; !!}";
@endsection

@section('content')
<main id="content" class="content loan-content" role="main">
    <div class="row">
        <div class="col-md-12 header"><img src="/assets/wallet_assets/images/logo.svg" /></div>
    </div>
    <div class="row">
        @if (empty($credit))
            @include('wallet.rejection_section', ['first_name' => $first_name, 'last_name' => $last_name, 'wallet_name' => 'UPay'])
        @else
            <div class="col-md-12 info-section text-center">
            @if($credit['amount'] == $walletInfo['amount'])
                <h4>Հարգելի {{ $first_name }} {{ $last_name }}, <br> Ձեր հայտը հաստատվում է <b>@number_to_money($credit['amount'])</b> ՀՀ դրամի չափով</h4>
            @else
                <h4>Հարգելի {{ $first_name }} {{ $last_name }}, <br> Ձեր հայտը բավարարվում է <b>@number_to_money($credit['amount'])</b> ՀՀ դրամի չափով</h4>
            @endif
            <hr>
            </div>
            <div class="col-md-12">
                <div class="loan-block">
                    <label for="notification_method">Ծանուցման եղանակ</label>
                    <select name="notification_method" id="notification_method" class="form-select">
                        @foreach(constants("NOTIFICATION_METHODS") as $key => $notification_method)
                            <option value="{{$key}}">{{$notification_method}}</option>
                        @endforeach
                    </select>
                </div>
            </div>
            <div class="col-md-12">
                <div class="loan-block">
                    <label for="dispute-resolution-method">Վեճերի լուծման եղանակ</label>
                    <select name="dispute_solution_method" id="dispute-resolution-method" class="form-select">
                        @foreach(constants("DISPUTE_SOLUTION_METHODS") as $key => $dispute_solution_method)
                            <option value="{{$key}}">{{$dispute_solution_method}}</option>
                        @endforeach
                    </select>
                </div>
            </div>
            <div class="col-md-12">
                <div class="checkbox loan-block">
                    <input class="agreement-checkbox" id="checkbox" type="checkbox" />
                    <label for="checkbox">Համաձայն եմ վարկավորման պայմանների հետ*</label>
                </div>
            </div>
            <div class="col-md-12 text-center">
                <button disabled type="submit" class="btn btn-primary upay-btn submit-button">
                    Ընդունել
                </button>
            </div>
        @endif
    </div>
</main>
@endsection
