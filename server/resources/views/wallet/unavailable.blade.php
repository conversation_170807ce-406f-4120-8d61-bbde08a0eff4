@extends('wallet.layout')

@section('content')
<main id="content" class="content loan-content" role="main">
    <div class="row">
        <div class="col-md-12 header"><img src="/assets/wallet_assets/images/logo.svg" /></div>
    </div>
    <div class="row">
        <div class="col-md-12 reject-section">
            <h4 class="error error-text">
                @if ($exception_code == 4065)
                    Խնդրում ենք ստուգել անձը հաստատող փաստաթղթի վավերականության ժամկետը
                @elseif ($exception_code == 4066)
                    Հայտը չի կարող ուսումնասիրվել սոց. քարտի բացակայության պատճառով
                @elseif ($exception_code == 4067)
                    Ձեր վարկային հայտը չի հաստատվել ավտոմատ համակարգի կողմից
                @elseif ($exception_code == 4078 || $exception_code == 4079)
                    Վարկի համար կարող եք դիմել Ձեր գործող պարտավորության գծով վճարումը կատարելուց հետո
                @elseif ($exception_code == 4080)
                    Ձեր տվյալները չեն կարող մշակվել ավտոմատ համակարգի կողմից
                @elseif ($exception_code == 4081)
                    Վարկի համար կարող եք դիմել Ձեր գործող պարտավորության գծով վճարումը կատարելուց հետո
                @elseif ($exception_code == 4100)
                    Կրկին վարկավորման համար խնդրում ենք դիմել մի փոքր ուշ։
                @elseif ($exception_code == 4062)
                    Խնդրում ենք ընտրել 20,000 և ավել գումար:
                @elseif ($exception_code == 4103)
                    Վարկավորման համար անհրաժեշտ է դիմել ՀՀ բջջային օպերատորների կողմից սպասարկվող հեռախոսահամարով։
                @else
                    Ձեր հարցումը ձախողվեց, խնդրում ենք փորձել ավելի ուշ
                @endif
            </h4>
        </div>
    </div>
</main>
@endsection
