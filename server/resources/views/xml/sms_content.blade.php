<bulk-request login="{{env('SMS_HTTP_SERVICE_LOGIN')}}" password="{{env('SMS_HTTP_SERVICE_PASSWORD')}}" ref-id="{{$refId}}" delivery-notification-requested="true" version="1.0">
	<message id="{{$id}}" msisdn="{{$phoneNumber}}" service-number="{{env('SMS_HTTP_SERVICE_NUMBER')}}" defer-date="{{$deferDate}}" validity-period="{{env('SMS_HTTP_SERVICE_VALIDITY_PERIOD')}}" priority="1">
		<content type="text/plain">{{$code}}</content>
	</message>
</bulk-request>